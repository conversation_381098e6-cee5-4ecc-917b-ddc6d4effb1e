import{S as De}from"./supplierModal-DiF_wNpr.js";import{_ as Ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{r as y,c as ye,a as we,h as d,b,o as p,d as u,g as a,w as n,j as s,l as de,D as Ie,G as $e,t as m,f as N,k as g,F as Pe,i as Te,u as Me,n as qe}from"./index-B4CbS3Hl.js";import{S as Re}from"./SettingOutlined-DSrSqAUc.js";const Oe={class:"material-table"},Ne={class:"table-operations"},Fe={class:"selection-summary"},Be={style:{color:"#666"}},Ae={class:"summary-content"},je=["onClick"],Ue=["onClick"],Qe=["onClick"],Ve=["onClick"],ze=["onClick"],Le={key:2,class:"quote-progress"},Ee={class:"quoted-count"},Ye={class:"rejected-count"},Ke={style:{margin:"12px"}},Je={__name:"materialTable",props:{searchForm:{type:Object,default:()=>({})}},emits:["search","reset"],setup(Se,{expose:q,emit:_}){const E=y(!1),K=()=>{E.value=!E.value},C=[{title:"商品名称",dataIndex:"name",key:"name",width:180,fixed:"left"},{title:"型号",dataIndex:"model",key:"model",width:180,fixed:"left"},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"期望交期",dataIndex:"expectedDelivery",key:"expectedDelivery",width:100},{title:"接受平替",dataIndex:"acceptAlternative",key:"acceptAlternative",width:100,customRender:({text:t})=>t?"是":"否"},{title:"询价状态",dataIndex:"status",key:"status",width:100},{title:"报价进度",dataIndex:"quoteProgress",key:"quoteProgress",width:250,customRender:({record:t})=>ae(t)},{title:"平替品牌",dataIndex:"alternativeBrand",key:"alternativeBrand",width:120,customRender:({text:t,record:e})=>e.acceptAlternative&&t?t:"-"},{title:"平替型号",dataIndex:"alternativeModel",key:"alternativeModel",width:120,customRender:({text:t,record:e})=>e.acceptAlternative&&t?t:"-"},{title:"单价 (¥)",dataIndex:"unitPrice",key:"unitPrice",width:100,customRender:({text:t,record:e})=>e.selectedSupplier?`¥${e.selectedSupplier.price.toFixed(2)}`:""},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:120,customRender:({text:t,record:e})=>e.selectedSupplier?`¥${e.selectedSupplier.totalPrice.toFixed(2)}`:""},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:100,customRender:({text:t,record:e})=>e.selectedSupplier?e.selectedSupplier.minOrderQuantity:""},{title:"交期",dataIndex:"delivery",key:"delivery",width:100,customRender:({text:t,record:e})=>e.selectedSupplier?e.selectedSupplier.promisedDelivery:""},{title:"询价单号",dataIndex:"rfqNo",key:"rfqNo",width:150,customRender:({text:t,record:e})=>e.status==="notStarted"?"-":t},{title:"询价时间",dataIndex:"rfqTime",key:"rfqTime",width:150,customRender:({text:t,record:e})=>e.status==="notStarted"?"-":t},{title:"结束时间",dataIndex:"endTime",key:"endTime",width:150,customRender:({text:t,record:e})=>e.status==="inProgress"?"-":t},{title:"截止时间",dataIndex:"deadline",key:"deadline",width:150},{title:"备注",dataIndex:"remark",key:"remark",width:150},{title:"操作",dataIndex:"action",key:"action",fixed:"right",width:275}],w=y(["name","model","brand","quantity","acceptAlternative","alternativeBrand","alternativeModel","unitPrice","totalPrice","expectedDelivery","rfqNo","rfqTime","deadline","status","quoteProgress","action"]),P=ye(()=>C.filter(t=>w.value.includes(t.dataIndex)||t.fixed)),k=t=>{w.value=t},Z=t=>{const e=[];return e.push({title:"供应商",dataIndex:"supplierName",key:"supplierName",width:150,customRender:({text:o,record:i})=>o||i.name||"-"}),t&&e.push({title:"平替品牌",dataIndex:"alternativeBrand",key:"alternativeBrand",width:120,customRender:({text:o,record:i})=>i.status==="pending"||i.status==="rejected"?"-":o||"-"},{title:"平替型号",dataIndex:"alternativeModel",key:"alternativeModel",width:120,customRender:({text:o,record:i})=>i.status==="pending"||i.status==="rejected"?"-":o||"-"}),e.push({title:"报价 (¥)",dataIndex:"price",key:"price",width:100,customRender:({text:o,record:i})=>i.status==="pending"||i.status==="rejected"?"-":o?`¥${o.toFixed(2)}`:""},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:100,customRender:({text:o,record:i})=>i.status==="pending"||i.status==="rejected"?"-":o?`¥${o.toFixed(2)}`:""},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:150},{title:"承诺交期",dataIndex:"promisedDelivery",key:"promisedDelivery",width:120,customRender:({text:o,record:i})=>i.status==="pending"||i.status==="rejected"?"-":o},{title:"有效期",dataIndex:"validityPeriod",key:"validityPeriod",width:120,customRender:({text:o,record:i})=>i.status==="pending"||i.status==="rejected"?"-":o},{title:"报价时间",dataIndex:"quoteTime",key:"quoteTime",width:150,customRender:({text:o,record:i})=>i.status==="pending"||i.status==="rejected"?"-":o},{title:"报价状态",dataIndex:"status",key:"status",width:100},{title:"报价类型",dataIndex:"quoteType",key:"quoteType",width:100,customRender:({text:o})=>o==="platform"?"平台报价":"外部报价"},{title:"备注",dataIndex:"remark",key:"remark",width:150},{title:"操作",dataIndex:"action",key:"action",width:100}),e},$=y([]),F=y(!1),U=we({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0}),I=y([]),D=t=>{I.value=t},c=ye(()=>parseFloat($.value.filter(t=>I.value.includes(t.id)).reduce((t,e)=>{const o=e.selectedSupplier?e.selectedSupplier.totalPrice:e.totalPrice||0;return t+o},0).toFixed(2))),B=(t,e)=>{if(e){e.suppliers.forEach(i=>{i.isSelected=!1});const o=e.suppliers.find(i=>i.id===t.id);o&&(o.isSelected=!0,e.selectedSupplier=o,e.acceptAlternative&&o.alternativeBrand&&o.alternativeModel&&(e.alternativeBrand=o.alternativeBrand,e.alternativeModel=o.alternativeModel))}},M=t=>({notStarted:"default",inProgress:"blue",accepted:"green",expired:"orange",invalid:"red",cancelled:"red"})[t]||"default",T=t=>({notStarted:"未开始",inProgress:"询价中",accepted:"已采纳",expired:"已过期",invalid:"已失效",cancelled:"已取消"})[t]||"未知",ee=t=>({pending:"blue",quoted:"green",rejected:"red",expired:"orange"})[t]||"default",te=t=>({pending:"待报价",quoted:"已报价",rejected:"已拒绝",expired:"已过期"})[t]||"未知",ae=t=>{if(!t.suppliers||t.suppliers.length===0)return"供应商数：0，已报价：0，已拒绝：0";const e=t.suppliers.length,o=t.suppliers.filter(R=>R.status==="quoted").length,i=t.suppliers.filter(R=>R.status==="rejected").length;return`供应商数：${e}，已报价：${o}，已拒绝：${i}`},J=t=>t.suppliers?t.suppliers.length:0,G=t=>t.suppliers?t.suppliers.filter(e=>e.status==="quoted").length:0,Q=t=>t.suppliers?t.suppliers.filter(e=>e.status==="rejected").length:0,V=t=>t==="notStarted",ne=t=>t==="notStarted",H=t=>t==="accepted",z=t=>["accepted","expired","invalid","cancelled"].includes(t),A=t=>t==="inProgress",le=t=>["notStarted","expired","invalid","cancelled"].includes(t),W=t=>{U.current=t.current,U.pageSize=t.pageSize,r()},r=()=>{F.value=!0,setTimeout(()=>{$.value=Array.from({length:10}).map((t,e)=>{const o=Math.floor(Math.random()*100+10),i=Math.floor(Math.random()*1e3+100),R=e%6===0?"notStarted":e%6===1?"inProgress":e%6===2?"accepted":e%6===3?"expired":e%6===4?"invalid":"cancelled",pe=Array.from({length:3}).map((O,x)=>{const re=x%4===0?"pending":x%4===1?"quoted":x%4===2?"rejected":"expired",fe=Math.floor(Math.random()*1e3+100),ve=x===1,_e=["兼容品牌A","兼容品牌B","通用品牌C"],ge=["ALT-001","COMP-002","GEN-003"],h=x%2===0?"platform":"external",v={platform:["严选供应商","深圳市电子科技有限公司","北京智能制造有限公司"],external:["上海精密器件有限公司","广州电子元件供应商","天津工业设备公司"]};return{id:`supplier-${e}-${x}`,name:`供应商 ${x+1}`,supplierName:v[h][x%v[h].length],price:fe,totalPrice:fe*o,minOrderQuantity:Math.floor(Math.random()*100+10),promisedDelivery:`${Math.floor(Math.random()*30+15)}天`,validityPeriod:"2023-07-05",quoteTime:"2023-06-05",status:re,remark:x%2===0?"含税价格":"",isSelected:!1,quoteType:h,alternativeBrand:ve&&re==="quoted"?_e[x%_e.length]:null,alternativeModel:ve&&re==="quoted"?ge[x%ge.length]:null}});let ie=null,me=null,Y=null;if(R==="accepted"){const O=pe.find(x=>x.status==="quoted");O&&(O.isSelected=!0,ie=O,e%3===0&&O.alternativeBrand&&O.alternativeModel&&(me=O.alternativeBrand,Y=O.alternativeModel))}return{id:`rfq-${e}`,name:`测试商品 ${e+1}`,model:`MODEL-${100+e}`,brand:e%3===0?"A":e%3===1?"B":"C",quantity:o,unitPrice:i,totalPrice:o*i,expectedDelivery:"30天",rfqNo:R==="notStarted"?"":`RFQ-2023-${1e3+e}`,rfqTime:R==="notStarted"?"":"2023-06-01",endTime:"2023-06-15",deadline:"2023-06-10",status:R,remark:e%2===0?"紧急采购":"",suppliers:pe,selectedSupplier:ie,acceptAlternative:e%3===0,alternativeBrand:me,alternativeModel:Y}}),U.total=100,F.value=!1},500)},l=t=>{console.log("删除询价单",t)},f=y(!1),L=y(!1),j=y(null),oe=t=>{j.value=t,f.value=!0},X=t=>{console.log("启动询价",t)},se=t=>{console.log("转采购单",t)},ue=t=>{console.log("再次询价",t)},he=t=>{console.log("取消",t)},ke=t=>{j.value&&t&&t.length>0&&(t.forEach(e=>{if(j.value.suppliers.findIndex(i=>i.id===e.id)===-1){const i={id:e.id,name:e.name,supplierName:e.name,price:null,totalPrice:null,minOrderQuantity:1,promisedDelivery:"",validityPeriod:"",quoteTime:"",status:"pending",remark:"",isSelected:!1,quoteType:"platform"};j.value.suppliers.push(i)}}),f.value=!1,alert(`已成功添加${t.length}个供应商`))},ce=()=>{f.value=!1},be=({key:t})=>{if(I.value.length===0)return alert("请先选择要操作的询价单");const e=$.value.filter(o=>I.value.includes(o.id));switch(t){case"startInquiry":console.log("批量启动询价",e);break;case"toPurchaseOrder":console.log("批量转采购单",e);break;case"cancel":console.log("批量取消",e);break;case"delete":console.log("批量删除",e);break}},xe=()=>{if(I.value.length===0)return alert("请先选择要分配的询价单");const t=$.value.filter(e=>I.value.includes(e.id));console.log("智能分配供应商",t)};return q({fetchData:r,selectedRowKeys:I,selectedTotalPrice:c}),r(),(t,e)=>{const o=d("a-button"),i=d("a-menu-item"),R=d("a-menu"),pe=d("a-dropdown"),ie=d("a-space"),me=d("a-tooltip"),Y=d("a-tag"),O=d("a-popconfirm"),x=d("a-table"),re=d("a-checkbox"),fe=d("a-col"),ve=d("a-row"),_e=d("a-checkbox-group"),ge=d("a-drawer");return p(),b("div",Oe,[u("div",Ne,[a(ie,null,{default:n(()=>[a(pe,null,{overlay:n(()=>[a(R,{onClick:be},{default:n(()=>[a(i,{key:"startInquiry"},{default:n(()=>e[3]||(e[3]=[s("启动询价")])),_:1}),a(i,{key:"toPurchaseOrder"},{default:n(()=>e[4]||(e[4]=[s("转采购单")])),_:1}),a(i,{key:"cancel"},{default:n(()=>e[5]||(e[5]=[s("取消")])),_:1}),a(i,{key:"delete"},{default:n(()=>e[6]||(e[6]=[s("删除")])),_:1})]),_:1})]),default:n(()=>[a(o,{type:"primary"},{default:n(()=>[e[2]||(e[2]=s(" 批量操作 ")),a(de(Ie))]),_:1})]),_:1}),a(o,{type:"primary",class:"smart-allocation-btn",onClick:xe},{default:n(()=>e[7]||(e[7]=[s("智能分配")])),_:1})]),_:1}),a(o,{onClick:K},{default:n(()=>[a(de(Re)),e[8]||(e[8]=s(" 列设置"))]),_:1})]),u("div",Fe,[u("div",null,[a(me,{placement:"top"},{title:n(()=>e[9]||(e[9]=[u("div",null,"1. 本表中的价格若未做特殊说明，均为含税价格。",-1),u("div",null,[s("2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下："),u("br"),s("订单总金额¥0.00 - ¥499.99，运费¥15.00"),u("br"),s("订单总金额¥500.00 - ¥999.99，运费¥8.00"),u("br"),s("订单总金额¥1000以上，免运费")],-1)])),default:n(()=>[u("span",Be,[a(de($e),{style:{"margin-right":"4px"}}),e[10]||(e[10]=s("价格与运费说明"))])]),_:1})]),u("div",Ae,[u("span",null,[e[11]||(e[11]=s("已选择：")),a(Y,{color:"red"},{default:n(()=>[s(m(I.value.length),1)]),_:1}),e[12]||(e[12]=s(" 个询价单"))]),u("span",null,[e[13]||(e[13]=s("总金额：")),a(Y,{color:"red"},{default:n(()=>[s("¥"+m(c.value.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]),_:1})])])]),a(x,{columns:P.value,"data-source":$.value,size:"middle",loading:F.value,pagination:U,onChange:W,"row-key":"id","row-selection":{selectedRowKeys:I.value,onChange:D},bordered:"",scroll:{x:1500}},{bodyCell:n(({column:h,record:v})=>[h.dataIndex==="action"?(p(),N(ie,{key:0},{default:n(()=>[V(v.status)?(p(),b("a",{key:0,onClick:S=>oe(v)},"配置供应商",8,je)):g("",!0),ne(v.status)?(p(),b("a",{key:1,onClick:S=>X(v)},"启动询价",8,Ue)):g("",!0),H(v.status)?(p(),b("a",{key:2,onClick:S=>se(v)},"转采购单",8,Qe)):g("",!0),z(v.status)?(p(),b("a",{key:3,onClick:S=>ue(v)},"再次询价",8,Ve)):g("",!0),A(v.status)?(p(),b("a",{key:4,onClick:S=>he(v),class:"danger-link"},"取消",8,ze)):g("",!0),le(v.status)?(p(),N(O,{key:5,title:"确定要删除此询价单吗?","ok-text":"确定","cancel-text":"取消",onConfirm:S=>l(v)},{default:n(()=>e[14]||(e[14]=[u("a",{class:"danger-link"},"删除",-1)])),_:2},1032,["onConfirm"])):g("",!0),e[15]||(e[15]=u("a",null,"询价历史",-1))]),_:2},1024)):g("",!0),h.dataIndex==="status"?(p(),N(Y,{key:1,color:M(v.status)},{default:n(()=>[s(m(T(v.status)),1)]),_:2},1032,["color"])):g("",!0),h.dataIndex==="quoteProgress"?(p(),b("div",Le,[u("span",null,"供应商数："+m(J(v))+"，",1),u("span",null,[e[16]||(e[16]=s("已报价：")),u("span",Ee,m(G(v)),1),e[17]||(e[17]=s("，"))]),u("span",null,[e[18]||(e[18]=s("已拒绝：")),u("span",Ye,m(Q(v)),1)])])):g("",!0)]),expandedRowRender:n(({record:h})=>[u("div",Ke,[a(x,{size:"small",columns:Z(h.acceptAlternative),"data-source":h.suppliers,pagination:!1,"row-key":"id",bordered:""},{bodyCell:n(({column:v,record:S})=>[v.dataIndex==="status"?(p(),N(Y,{key:0,color:ee(S.status)},{default:n(()=>[s(m(te(S.status)),1)]),_:2},1032,["color"])):g("",!0),v.dataIndex==="action"?(p(),N(o,{key:1,type:"link",disabled:S.isSelected||S.status==="pending"||S.status==="rejected"||S.quoteType==="external",onClick:bt=>B(S,h)},{default:n(()=>[s(m(S.isSelected?"已选择":"选择"),1)]),_:2},1032,["type","disabled","onClick"])):g("",!0)]),_:2},1032,["columns","data-source"])])]),_:1},8,["columns","data-source","loading","pagination","row-selection"]),a(ge,{title:"配置表格列",placement:"right",visible:E.value,onClose:K,width:"400px"},{default:n(()=>[a(_e,{value:w.value,"onUpdate:value":e[0]||(e[0]=h=>w.value=h),onChange:k},{default:n(()=>[a(ve,null,{default:n(()=>[(p(),b(Pe,null,Te(C,h=>a(fe,{span:12,key:h.dataIndex},{default:n(()=>[a(re,{value:h.dataIndex,disabled:h.fixed},{default:n(()=>[s(m(h.title),1)]),_:2},1032,["value","disabled"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"])]),_:1},8,["visible"]),a(De,{title:"配置供应商",visible:f.value,"confirm-loading":L.value,"onUpdate:visible":e[1]||(e[1]=h=>f.value=h),onOk:ke,onCancel:ce},null,8,["visible","confirm-loading"])])}}},Ge=Ce(Je,[["__scopeId","data-v-aa917306"]]),He={class:"order-table"},We={class:"table-operations"},Xe={class:"selection-summary"},Ze={style:{color:"#666"}},et={class:"summary-content"},tt=["onClick"],at={key:1},nt={key:2},lt={key:3,style:{"font-weight":"bold",color:"#f5222d"}},ot=["onClick"],st=["onClick"],it=["onClick"],rt=["onClick"],dt={class:"share-content"},ut={class:"share-text"},ct={class:"share-actions"},pt={__name:"orderTable",props:{searchForm:{type:Object,default:()=>({})}},emits:["viewDetail"],setup(Se,{expose:q,emit:_}){const E=Me(),K=[{title:"询价单号",dataIndex:"rfqNo",key:"rfqNo",width:160,fixed:"left"},{title:"物料型号数",dataIndex:"materialModelCount",key:"materialModelCount",width:110,align:"center"},{title:"物料总数",dataIndex:"materialCount",key:"materialCount",width:100,align:"center"},{title:"物料总价",dataIndex:"totalPrice",key:"totalPrice",width:140,align:"right"},{title:"询价时间",dataIndex:"rfqTime",key:"rfqTime",width:120},{title:"截止时间",dataIndex:"deadline",key:"deadline",width:120},{title:"操作",dataIndex:"action",key:"action",fixed:"right",width:200}],C=y([]),w=y(!1),P=we({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0}),k=y([]),Z=r=>{k.value=r},$=ye(()=>parseFloat(C.value.filter(r=>k.value.includes(r.rfqNo)).reduce((r,l)=>r+l.totalPrice,0).toFixed(2))),F=r=>r==="accepted",U=r=>["accepted","deadline","expired","cancelled"].includes(r),I=r=>r==="inProgress",D=r=>["deadline","expired","cancelled"].includes(r),c=r=>{P.current=r.current,P.pageSize=r.pageSize,B()},B=()=>{w.value=!0,setTimeout(()=>{const r=[],l=["inProgress","accepted","deadline","expired","cancelled"];for(let f=0;f<15;f++){const L=l[f%l.length],j=Math.floor(Math.random()*8)+2,oe=Math.floor(Math.random()*5)+1,se=(Math.floor(Math.random()*5e3)+1e3)*j;r.push({rfqNo:`RFQ202300${1e3+f}`,materialCount:j,materialModelCount:oe,totalPrice:se,rfqTime:"2023-06-0"+(f%9+1),deadline:"2023-06-"+(10+f%20),status:L,materials:[]})}C.value=r,P.total=50,w.value=!1},500)},M=r=>{E.push(`/workspace/purchase/rfq/detail/${r.rfqNo}`)},T=r=>{console.log("转采购单",r)},ee=r=>{console.log("再次询价",r)},te=r=>{console.log("取消询价单",r)},ae=r=>{console.log("删除询价单",r),B()},J=({key:r})=>{if(k.value.length===0)return alert("请先选择要操作的询价单");const l=C.value.filter(f=>k.value.includes(f.rfqNo));switch(r){case"toPurchaseOrder":console.log("批量转采购单",l);break;case"reInquiry":console.log("批量再次询价",l);break;case"cancel":console.log("批量取消",l);break;case"delete":console.log("批量删除",l);break}},G=()=>{if(k.value.length===0)return alert("请先选择要转换的询价单");const l=C.value.filter(f=>k.value.includes(f.rfqNo)).filter(f=>F(f.status));if(l.length===0)return alert("选中的询价单中没有可转换的记录（只有已采纳状态的询价单可以转换）");console.log("批量转采购单",l)},Q=y(!1),V=y(null),ne=y({name:"张三"}),H=y({fullName:"深圳市研选科技有限公司"}),z=ye(()=>{if(!V.value)return"";const r="http://prototype.yanxuan.icu/bomai/workSpace/company-space/ans-price",l=btoa(JSON.stringify({rfqNo:V.value.rfqNo,timestamp:Date.now()}));return`${r}?param=${l}`}),A=r=>{V.value=r,Q.value=!0},le=async()=>{try{await navigator.clipboard.writeText(z.value),qe.success("链接已复制到剪贴板")}catch{const l=document.createElement("textarea");l.value=z.value,document.body.appendChild(l),l.select(),document.execCommand("copy"),document.body.removeChild(l),qe.success("链接已复制到剪贴板")}},W=()=>{Q.value=!1,V.value=null};return q({fetchData:B,selectedRowKeys:k,selectedTotalPrice:$}),B(),(r,l)=>{const f=d("a-button"),L=d("a-menu-item"),j=d("a-menu"),oe=d("a-dropdown"),X=d("a-space"),se=d("a-tooltip"),ue=d("a-tag"),he=d("a-popconfirm"),ke=d("a-table"),ce=d("a-typography-paragraph"),be=d("a-typography-text"),xe=d("a-alert"),t=d("a-modal");return p(),b("div",He,[u("div",We,[a(X,null,{default:n(()=>[a(oe,null,{overlay:n(()=>[a(j,{onClick:J},{default:n(()=>[a(L,{key:"toPurchaseOrder"},{default:n(()=>l[2]||(l[2]=[s("转采购单")])),_:1}),a(L,{key:"reInquiry"},{default:n(()=>l[3]||(l[3]=[s("再次询价")])),_:1}),a(L,{key:"cancel"},{default:n(()=>l[4]||(l[4]=[s("取消")])),_:1}),a(L,{key:"delete"},{default:n(()=>l[5]||(l[5]=[s("删除")])),_:1})]),_:1})]),default:n(()=>[a(f,{type:"primary"},{default:n(()=>[l[1]||(l[1]=s(" 批量操作 ")),a(de(Ie))]),_:1})]),_:1}),a(f,{type:"primary",onClick:G},{default:n(()=>l[6]||(l[6]=[s("批量转采购单")])),_:1})]),_:1})]),u("div",Xe,[u("div",null,[a(se,{placement:"top"},{title:n(()=>l[7]||(l[7]=[u("div",null,"1. 物料总价为各询价单内已选择供应商的物料价格汇总。",-1),u("div",null,"2. 询价单状态包括：未开始、询价中、已采纳、已过期、已失效、已取消。",-1)])),default:n(()=>[u("span",Ze,[a(de($e),{style:{"margin-right":"4px"}}),l[8]||(l[8]=s("询价单说明"))])]),_:1})]),u("div",et,[u("span",null,[l[9]||(l[9]=s("已选择：")),a(ue,{color:"red"},{default:n(()=>[s(m(k.value.length),1)]),_:1}),l[10]||(l[10]=s(" 个询价单"))]),u("span",null,[l[11]||(l[11]=s("总金额：")),a(ue,{color:"red"},{default:n(()=>[s("¥"+m($.value.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]),_:1})])])]),a(ke,{columns:K,"data-source":C.value,size:"middle",loading:w.value,pagination:P,onChange:c,"row-key":"rfqNo","row-selection":{selectedRowKeys:k.value,onChange:Z},bordered:""},{bodyCell:n(({column:e,record:o})=>[e.dataIndex==="rfqNo"?(p(),b("a",{key:0,onClick:i=>M(o),style:{color:"#1890ff"}},m(o.rfqNo),9,tt)):g("",!0),e.dataIndex==="materialCount"?(p(),b("span",at,m(o.materialCount),1)):g("",!0),e.dataIndex==="materialModelCount"?(p(),b("span",nt,m(o.materialModelCount),1)):g("",!0),e.dataIndex==="totalPrice"?(p(),b("span",lt,"¥"+m(o.totalPrice.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)):g("",!0),e.dataIndex==="action"?(p(),N(X,{key:4},{default:n(()=>[F(o.status)?(p(),b("a",{key:0,onClick:i=>T(o)},"转采购单",8,ot)):g("",!0),U(o.status)?(p(),b("a",{key:1,onClick:i=>ee(o)},"再次询价",8,st)):g("",!0),I(o.status)?(p(),b("a",{key:2,onClick:i=>te(o),class:"danger-link"},"取消",8,it)):g("",!0),D(o.status)?(p(),N(he,{key:3,title:"确定要删除此询价单吗?","ok-text":"确定","cancel-text":"取消",onConfirm:i=>ae(o)},{default:n(()=>l[12]||(l[12]=[u("a",{class:"danger-link"},"删除",-1)])),_:2},1032,["onConfirm"])):g("",!0),u("a",{onClick:i=>A(o)},"分享",8,rt)]),_:2},1024)):g("",!0)]),_:1},8,["data-source","loading","pagination","row-selection"]),a(t,{visible:Q.value,"onUpdate:visible":l[0]||(l[0]=e=>Q.value=e),title:"分享询价单",width:"600px",footer:null,onCancel:W},{default:n(()=>[u("div",dt,[u("div",ut,[a(ce,null,{default:n(()=>[u("strong",null,m(ne.value.name),1),l[13]||(l[13]=s("邀请您加入研选工场，并查看")),u("strong",null,m(H.value.fullName),1),l[14]||(l[14]=s("的询价单。 "))]),_:1}),a(ce,null,{default:n(()=>[l[15]||(l[15]=s(" 点击链接关注最新报价: ")),a(be,{copyable:"","copy-config":{text:z.value},type:"success"},{default:n(()=>[s(m(z.value),1)]),_:1},8,["copy-config"])]),_:1})]),a(xe,{message:"本链接为公开链接，任何研选工场会员均可查看报价，请谨慎处理。",type:"warning","show-icon":""}),u("div",ct,[a(X,null,{default:n(()=>[a(f,{type:"primary",onClick:le},{default:n(()=>l[16]||(l[16]=[s("复制链接")])),_:1}),a(f,{onClick:W},{default:n(()=>l[17]||(l[17]=[s("关闭")])),_:1})]),_:1})])])]),_:1},8,["visible"])])}}},mt=Ce(pt,[["__scopeId","data-v-2f38db7e"]]),ft={class:"rfq-container"},vt={class:"search-area"},_t={class:"table-area"},gt={class:"view-switch"},yt={class:"order-detail"},ht={style:{"margin-top":"20px"}},kt={__name:"index",setup(Se){const q=y("material"),_=we({model:"",brand:void 0,category:void 0,rfqNo:"",rfqTimeRange:[],deadlineRange:[]}),E=y([{label:"品牌A",value:"A"},{label:"品牌B",value:"B"},{label:"品牌C",value:"C"}]),K=y([{title:"电子元件",value:"electronics",children:[{title:"集成电路",value:"ic"},{title:"电阻电容",value:"passive"}]},{title:"机械零件",value:"mechanical",children:[{title:"螺丝螺栓",value:"screws"},{title:"轴承",value:"bearings"}]}]),C=y(null),w=y(null),P=y(!1),k=y({}),Z=()=>{console.log("切换到",q.value,"视图"),q.value==="material"&&C.value?C.value.fetchData():q.value==="order"&&w.value&&w.value.fetchData()},$=()=>{console.log("搜索条件:",_),q.value==="material"&&C.value?C.value.fetchData():q.value==="order"&&w.value&&w.value.fetchData()},F=()=>{Object.keys(_).forEach(D=>{Array.isArray(_[D])?_[D]=[]:_[D]=void 0}),$()},U=D=>{k.value=D,P.value=!0},I=()=>{P.value=!1,k.value={}};return(D,c)=>{const B=d("a-input"),M=d("a-form-item"),T=d("a-col"),ee=d("a-select-option"),te=d("a-select"),ae=d("a-tree-select"),J=d("a-range-picker"),G=d("a-button"),Q=d("a-space"),V=d("a-row"),ne=d("a-form"),H=d("a-radio-button"),z=d("a-radio-group"),A=d("a-descriptions-item"),le=d("a-descriptions"),W=d("a-alert"),r=d("a-modal");return p(),b("div",ft,[u("div",vt,[a(ne,{layout:"inline",model:_},{default:n(()=>[a(V,{gutter:12},{default:n(()=>[a(T,{span:8},{default:n(()=>[a(M,{label:"产品型号"},{default:n(()=>[a(B,{value:_.model,"onUpdate:value":c[0]||(c[0]=l=>_.model=l),placeholder:"请输入型号"},null,8,["value"])]),_:1})]),_:1}),a(T,{span:8},{default:n(()=>[a(M,{label:"品牌"},{default:n(()=>[a(te,{value:_.brand,"onUpdate:value":c[1]||(c[1]=l=>_.brand=l),placeholder:"请选择品牌",style:{width:"100%"},allowClear:""},{default:n(()=>[(p(!0),b(Pe,null,Te(E.value,l=>(p(),N(ee,{key:l.value,value:l.value},{default:n(()=>[s(m(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value"])]),_:1})]),_:1}),a(T,{span:8},{default:n(()=>[a(M,{label:"产品分类"},{default:n(()=>[a(ae,{value:_.category,"onUpdate:value":c[2]||(c[2]=l=>_.category=l),style:{width:"100%"},"dropdown-style":{maxHeight:"400px",overflow:"auto"},"tree-data":K.value,placeholder:"请选择产品分类","tree-default-expand-all":"","allow-clear":""},null,8,["value","tree-data"])]),_:1})]),_:1}),a(T,{span:8},{default:n(()=>[a(M,{label:"询价单号"},{default:n(()=>[a(B,{value:_.rfqNo,"onUpdate:value":c[3]||(c[3]=l=>_.rfqNo=l),placeholder:"请输入询价单号"},null,8,["value"])]),_:1})]),_:1}),a(T,{span:8},{default:n(()=>[a(M,{label:"询价时间"},{default:n(()=>[a(J,{value:_.rfqTimeRange,"onUpdate:value":c[4]||(c[4]=l=>_.rfqTimeRange=l),format:"YYYY-MM-DD"},null,8,["value"])]),_:1})]),_:1}),a(T,{span:8},{default:n(()=>[a(M,{label:"截止时间"},{default:n(()=>[a(J,{value:_.deadlineRange,"onUpdate:value":c[5]||(c[5]=l=>_.deadlineRange=l),format:"YYYY-MM-DD"},null,8,["value"])]),_:1})]),_:1}),a(T,{span:2},{default:n(()=>[a(Q,null,{default:n(()=>[a(G,{type:"primary",onClick:$},{default:n(()=>c[8]||(c[8]=[s("查询")])),_:1}),a(G,{onClick:F},{default:n(()=>c[9]||(c[9]=[s("重置")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),u("div",_t,[u("div",gt,[a(z,{value:q.value,"onUpdate:value":c[6]||(c[6]=l=>q.value=l),"button-style":"solid",onChange:Z},{default:n(()=>[a(H,{value:"material"},{default:n(()=>c[10]||(c[10]=[s("物料视图")])),_:1}),a(H,{value:"order"},{default:n(()=>c[11]||(c[11]=[s("单据视图")])),_:1})]),_:1},8,["value"])]),q.value==="material"?(p(),N(Ge,{key:0,ref_key:"materialTableRef",ref:C,searchForm:_,onSearch:$,onReset:F},null,8,["searchForm"])):g("",!0),q.value==="order"?(p(),N(mt,{key:1,ref_key:"orderTableRef",ref:w,searchForm:_,onViewDetail:U},null,8,["searchForm"])):g("",!0)]),a(r,{visible:P.value,"onUpdate:visible":c[7]||(c[7]=l=>P.value=l),title:"询价单详情",width:"1200px",footer:null,onCancel:I},{default:n(()=>[u("div",yt,[a(le,{column:3,bordered:""},{default:n(()=>[a(A,{label:"询价单号"},{default:n(()=>[s(m(k.value.rfqNo),1)]),_:1}),a(A,{label:"物料总数"},{default:n(()=>[s(m(k.value.materialCount)+" 种",1)]),_:1}),a(A,{label:"物料总价"},{default:n(()=>{var l;return[s("¥"+m((l=k.value.totalPrice)==null?void 0:l.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]}),_:1}),a(A,{label:"询价时间"},{default:n(()=>[s(m(k.value.rfqTime),1)]),_:1}),a(A,{label:"截止时间"},{default:n(()=>[s(m(k.value.deadline),1)]),_:1})]),_:1}),u("div",ht,[c[12]||(c[12]=u("h4",null,"物料明细",-1)),a(W,{message:"此处可显示该询价单下的具体物料信息，包括每个物料的供应商报价情况等详细信息。",type:"info","show-icon":""})])])]),_:1},8,["visible"])])}}},qt=Ce(kt,[["__scopeId","data-v-22531f30"]]);export{qt as default};
