import{_ as ie}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as re}from"./SettingOutlined-DSrSqAUc.js";import{r as C,c as se,m as ue,b as v,d as R,g as n,w as t,h as r,j as c,l as ee,k as M,F as $,t as b,i as ne,o as s,D as ye,f as x,a as ce,u as ve}from"./index-B4CbS3Hl.js";import{O as he,A as _e}from"./OrderedListOutlined-UAwTKBXr.js";const be={class:"material-table"},ke={class:"table-operations"},fe=["onClick"],Ce=["onClick"],Se=["onClick"],we={__name:"materialTable",props:{tableData:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},pagination:{type:Object,default:()=>({})}},emits:["tableChange","selectChange","export","print","viewDetail","edit","cancel","columnsChange"],setup(B,{emit:z}){const S=z,D=[{title:"物料名称",dataIndex:"materialName",key:"materialName",sorter:!0},{title:"型号",dataIndex:"model",key:"model",sorter:!0},{title:"品牌",dataIndex:"brand",key:"brand",sorter:!0},{title:"数量",dataIndex:"quantity",key:"quantity",sorter:!0},{title:"送货单号",dataIndex:"receiptNo",key:"receiptNo",sorter:!0},{title:"所属订单",dataIndex:"poNo",key:"poNo",sorter:!0},{title:"操作",dataIndex:"action",key:"action",fixed:"right",width:100}],O=C(D.map(l=>l.dataIndex)),A=se(()=>D.filter(l=>O.value.includes(l.dataIndex))),L=C(!1),H=()=>{L.value=!0},_=l=>{O.value=l,S("columnsChange",A.value)},j=(l,m,I)=>{S("tableChange",{pagination:l,filters:m,sorter:I})},K=C([]),V=l=>{K.value=l,S("selectChange",l)},U=l=>{console.log("查看采购订单:",l.poNo)},T=l=>{if(l&&l.startsWith("DN")&&l.length>=16)return l;const m=new Date,I=m.getFullYear(),Y=String(m.getMonth()+1).padStart(2,"0"),q=String(m.getDate()).padStart(2,"0"),Q=String(m.getHours()).padStart(2,"0"),W=String(m.getMinutes()).padStart(2,"0");String(m.getSeconds()).padStart(2,"0");const P=l?l.match(/\d+/):null,p=P?P[0].slice(-2).padStart(2,"0"):String(Math.floor(Math.random()*100)).padStart(2,"0");return`DN${I}${Y}${q}${Q}${W}${p}`};return ue(()=>{}),(l,m)=>{const I=r("a-button"),Y=r("a-space"),q=r("a-table"),Q=r("a-checkbox"),W=r("a-checkbox-group"),P=r("a-drawer");return s(),v("div",be,[R("div",ke,[n(Y,null,{default:t(()=>[n(I,{onClick:H},{icon:t(()=>[n(ee(re))]),default:t(()=>[m[2]||(m[2]=c("列设置 "))]),_:1})]),_:1})]),n(q,{dataSource:B.tableData,columns:A.value,rowKey:p=>p.id,pagination:B.pagination,loading:B.loading,rowSelection:{selectedRowKeys:K.value,onChange:V},onChange:j},{bodyCell:t(({column:p,record:e})=>[p.dataIndex==="materialName"?(s(),v($,{key:0},[c(b(e.materialName),1)],64)):p.dataIndex==="model"?(s(),v($,{key:1},[c(b(e.model),1)],64)):p.dataIndex==="brand"?(s(),v($,{key:2},[c(b(e.brand),1)],64)):p.dataIndex==="quantity"?(s(),v($,{key:3},[c(b(e.quantity),1)],64)):p.dataIndex==="receiptNo"?(s(),v("a",{key:4,onClick:o=>l.$emit("viewDetail",e)},b(T(e.receiptNo)),9,fe)):p.dataIndex==="poNo"?(s(),v("a",{key:5,onClick:o=>U(e)},b(e.poNo),9,Ce)):p.dataIndex==="action"?(s(),v("a",{key:6,onClick:o=>l.$emit("cancel",e)},"退货",8,Se)):M("",!0)]),_:1},8,["dataSource","columns","rowKey","pagination","loading","rowSelection"]),n(P,{title:"列设置",placement:"right",visible:L.value,onClose:m[1]||(m[1]=p=>L.value=!1),width:"300px"},{default:t(()=>[n(W,{value:O.value,"onUpdate:value":m[0]||(m[0]=p=>O.value=p),onChange:_},{default:t(()=>[(s(),v($,null,ne(D,p=>R("div",{key:p.dataIndex,style:{"margin-bottom":"8px"}},[n(Q,{value:p.dataIndex},{default:t(()=>[c(b(p.title),1)]),_:2},1032,["value"])])),64))]),_:1},8,["value"])]),_:1},8,["visible"])])}}},xe=ie(we,[["__scopeId","data-v-83823119"]]),$e={class:"order-table"},De={class:"table-operations"},Ie={class:"operations-left"},Ne={class:"operations-right"},Me={style:{margin:"12px"}},Re=["onClick"],Te=["onClick"],Fe=["onClick"],Oe=["onClick"],Ae={__name:"orderTable",props:{tableData:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},pagination:{type:Object,default:()=>({})}},emits:["tableChange","selectChange","viewDetail","receipt","return","columnsChange","batchReceipt","batchReturn","materialReturn","viewPo"],setup(B,{emit:z}){const S=z,D=C([]),O=(e,o)=>{e?D.value=[...D.value,o.id]:D.value=D.value.filter(w=>w!==o.id)},A=[{title:"物料名称",dataIndex:"materialName",key:"materialName"},{title:"型号",dataIndex:"model",key:"model"},{title:"品牌",dataIndex:"brand",key:"brand"},{title:"数量",dataIndex:"quantity",key:"quantity"},{title:"采购单号",dataIndex:"poNo",key:"poNo"},{title:"操作",dataIndex:"operation",key:"operation"}],L=se(()=>A),H=[{title:"送货单号",dataIndex:"receiptNo",key:"receiptNo"},{title:"状态",dataIndex:"status",key:"status",width:120},{title:"物料数量",dataIndex:"materialCount",key:"materialCount",width:150},{title:"发货时间",dataIndex:"deliveryTime",key:"deliveryTime",width:180},{title:"物流单数量",dataIndex:"logisticsCount",key:"logisticsCount",width:120},{title:"操作",dataIndex:"action",key:"action",fixed:"right",width:200}],_=C(H.map(e=>e.dataIndex)),j=se(()=>H.filter(e=>_.value.includes(e.dataIndex))),K=C(!1),V=()=>{K.value=!0},U=e=>{_.value=e,S("columnsChange",j.value)},T=(e,o,w)=>{S("tableChange",{pagination:e,filters:o,sorter:w})},l=C([]),m=e=>{l.value=e,S("selectChange",e)},I=e=>{switch(e){case"shipped":return"blue";case"received":return"green";case"returning":return"red";case"partial_returned":return"cyan";default:return"default"}},Y=e=>{switch(e){case"shipped":return"已发货";case"received":return"已收货";case"returning":return"退货中";case"partial_returned":return"已收货（部分退货）";default:return"未知状态"}},q=e=>{switch(e){case"已发货":return"green";case"运输中":return"blue";case"已签收":return"purple";case"配送中":return"cyan";default:return"default"}},Q=e=>{switch(e){case"已付款":return"green";case"部分付款":return"orange";case"待付款":return"red";default:return"default"}},W=e=>{S("materialReturn",e)},P=e=>{S("viewPo",e)},p=e=>{if(e&&e.startsWith("DN")&&e.length>=16)return e;const o=new Date,w=o.getFullYear(),J=String(o.getMonth()+1).padStart(2,"0"),Z=String(o.getDate()).padStart(2,"0"),te=String(o.getHours()).padStart(2,"0"),G=String(o.getMinutes()).padStart(2,"0");String(o.getSeconds()).padStart(2,"0");const X=e?e.match(/\d+/):null,ae=X?X[0].slice(-2).padStart(2,"0"):String(Math.floor(Math.random()*100)).padStart(2,"0");return`DN${w}${J}${Z}${te}${G}${ae}`};return ue(()=>{}),(e,o)=>{const w=r("a-button"),J=r("a-menu-item"),Z=r("a-menu"),te=r("a-dropdown"),G=r("a-tag"),X=r("a-table"),ae=r("a-space"),a=r("a-checkbox"),u=r("a-checkbox-group"),y=r("a-drawer");return s(),v("div",$e,[R("div",De,[R("div",Ie,[n(te,null,{overlay:t(()=>[n(Z,null,{default:t(()=>[n(J,{key:"receipt",onClick:o[0]||(o[0]=i=>e.$emit("batchReceipt"))},{default:t(()=>o[5]||(o[5]=[c(" 收货 ")])),_:1}),n(J,{key:"return",onClick:o[1]||(o[1]=i=>e.$emit("batchReturn"))},{default:t(()=>o[6]||(o[6]=[c(" 退货 ")])),_:1})]),_:1})]),default:t(()=>[n(w,{type:"primary"},{default:t(()=>[o[4]||(o[4]=c(" 批量操作 ")),n(ee(ye))]),_:1})]),_:1})]),R("div",Ne,[n(w,{onClick:V},{icon:t(()=>[n(ee(re))]),default:t(()=>[o[7]||(o[7]=c("列设置 "))]),_:1})])]),n(X,{dataSource:B.tableData,columns:j.value,rowKey:i=>i.id,pagination:B.pagination,loading:B.loading,rowSelection:{selectedRowKeys:l.value,onChange:m},onChange:T,expandable:{expandedRowKeys:D.value,onExpand:O,expandRowByClick:!0}},{expandedRowRender:t(({record:i})=>[R("div",Me,[n(X,{columns:L.value,"data-source":i.materialItems,pagination:!1,"row-key":"id",bordered:"",size:"small"},{bodyCell:t(({column:d,text:f,record:h})=>[d.dataIndex==="unitPrice"||d.dataIndex==="totalPrice"?(s(),v($,{key:0},[c(b(parseFloat(f).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)],64)):M("",!0),d.dataIndex==="shippedQuantity"||d.dataIndex==="receivedQuantity"||d.dataIndex==="cancelledQuantity"?(s(),v($,{key:1},[c(b(f||0),1)],64)):M("",!0),d.dataIndex==="expectedArrivalTime"?(s(),v($,{key:2},[c(b(h.expectedArrivalTime?h.expectedArrivalTime:"-"),1)],64)):M("",!0),d.dataIndex==="logisticsStatus"?(s(),x(G,{key:3,color:q(h.logisticsStatus)},{default:t(()=>[c(b(h.logisticsStatus||"-"),1)]),_:2},1032,["color"])):M("",!0),d.dataIndex==="financialStatus"?(s(),x(G,{key:4,color:Q(h.financialStatus)},{default:t(()=>[c(b(h.financialStatus||"-"),1)]),_:2},1032,["color"])):M("",!0),d.dataIndex==="poNo"?(s(),v("a",{key:5,onClick:k=>P(h.poNo)},b(h.poNo),9,Re)):M("",!0),d.dataIndex==="operation"?(s(),x(w,{key:6,type:"link",size:"small",onClick:k=>W(h)},{default:t(()=>o[8]||(o[8]=[c(" 退货 ")])),_:2},1032,["onClick"])):M("",!0)]),_:2},1032,["columns","data-source"])])]),bodyCell:t(({column:i,record:d})=>[i.dataIndex==="receiptNo"?(s(),v("a",{key:0,onClick:f=>e.$emit("viewDetail",d)},b(p(d.receiptNo)),9,Te)):M("",!0),i.dataIndex==="status"?(s(),x(G,{key:1,color:I(d.status)},{default:t(()=>[c(b(Y(d.status)),1)]),_:2},1032,["color"])):i.dataIndex==="action"?(s(),x(ae,{key:2},{default:t(()=>[R("a",{onClick:f=>e.$emit("receipt",d)},"收货",8,Fe),R("a",{onClick:f=>e.$emit("return",d)},"退货",8,Oe)]),_:2},1024)):M("",!0)]),_:1},8,["dataSource","columns","rowKey","pagination","loading","rowSelection","expandable"]),n(y,{title:"列设置",placement:"right",visible:K.value,onClose:o[3]||(o[3]=i=>K.value=!1),width:"300px"},{default:t(()=>[n(u,{value:_.value,"onUpdate:value":o[2]||(o[2]=i=>_.value=i),onChange:U},{default:t(()=>[(s(),v($,null,ne(H,i=>R("div",{key:i.dataIndex,style:{"margin-bottom":"8px"}},[n(a,{value:i.dataIndex},{default:t(()=>[c(b(i.title),1)]),_:2},1032,["value"])])),64))]),_:1},8,["value"])]),_:1},8,["visible"])])}}},Ee=ie(Ae,[["__scopeId","data-v-460548dd"]]),Be={class:"receipt-container"},Ke={class:"search-area"},Ve={class:"view-selector"},Ue={__name:"index",setup(B){const z=C(!1),S=()=>{z.value=!z.value},D=ve(),O=[{key:"receiptNo",label:"送货单号",type:"input"},{key:"materialName",label:"物料名称",type:"input"},{key:"materialModel",label:"物料型号",type:"input"},{key:"deliveryTimeRange",label:"发货时间",type:"dateRange"},{key:"status",label:"状态",type:"select",options:[{label:"已发货",value:"shipped"},{label:"已收货",value:"received"},{label:"退货中",value:"returning"},{label:"已收货（部分退货）",value:"partial_returned"}]},{key:"poNo",label:"采购订单号",type:"input"},{key:"deliveryNo",label:"送货单号",type:"input"},{key:"category",label:"物料分类",type:"select",options:[{label:"电子元件",value:"electronics"},{label:"机械零件",value:"mechanical"},{label:"原材料",value:"raw"}]},{key:"brand",label:"品牌",type:"input"}],A=C(["receiptNo","status","deliveryTimeRange"]),L=se(()=>O.filter(a=>A.value.includes(a.key))),H=a=>{A.value=a},_=ce({receiptNo:"",materialName:"",materialModel:"",deliveryTimeRange:[],status:void 0,poNo:"",deliveryNo:"",category:void 0,brand:""}),j=()=>{T.current=1,o()},K=()=>{Object.keys(_).forEach(a=>{Array.isArray(_[a])?_[a]=[]:_[a]=void 0}),j()},V=C([]),U=C(!1),T=ce({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0}),l=C("order"),m=a=>{T.current=a.current,T.pageSize=a.pageSize,o()},I=C([]),Y=a=>{I.value=a},q=a=>{l.value=a,o()},Q=a=>{console.log("Material columns changed:",a)},W=a=>{console.log("Order columns changed:",a)},P=a=>{const u=a.getFullYear(),y=(a.getMonth()+1).toString().padStart(2,"0"),i=a.getDate().toString().padStart(2,"0"),d=a.getHours().toString().padStart(2,"0"),f=a.getMinutes().toString().padStart(2,"0");return`${u}-${y}-${i} ${d}:${f}`},p=(a,u)=>{const y=new Date(a.getTime()+u*60*1e3),i=y.getFullYear(),d=(y.getMonth()+1).toString().padStart(2,"0"),f=y.getDate().toString().padStart(2,"0"),h=y.getHours().toString().padStart(2,"0"),k=y.getMinutes().toString().padStart(2,"0"),N=y.getSeconds().toString().padStart(2,"0");return`DN${i}${d}${f}${h}${k}${N}`},e=(a,u)=>{const y=new Date(a.getTime()+u*30*1e3),i=y.getFullYear(),d=(y.getMonth()+1).toString().padStart(2,"0"),f=y.getDate().toString().padStart(2,"0"),h=y.getHours().toString().padStart(2,"0"),k=y.getMinutes().toString().padStart(2,"0"),N=y.getSeconds().toString().padStart(2,"0");return`SO${i}${d}${f}${h}${k}${N}`},o=()=>{U.value=!0,setTimeout(()=>{const a=[],u=[];Array.from({length:10}).forEach((y,i)=>{const d=["shipped","received","returning","partial_returned"],f=d[i%d.length],h=new Date(Date.now()-Math.random()*10*24*60*60*1e3),k={id:`receipt-${i}`,receiptNo:p(h,i),status:f,deliveryTime:P(h),logisticsCount:Math.floor(Math.random()*3)+1,materialCount:0,materialItems:[]},N=Math.floor(Math.random()*5)+1;k.materialCount=0,Array.from({length:N}).forEach((de,F)=>{const oe=Math.floor(Math.random()*100)+1;k.materialCount+=oe;const le={id:`material-${i}-${F}`,receiptNo:k.receiptNo,materialName:`测试物料 ${F+1}`,model:`MODEL-${1e3+F}`,brand:F%3===0?"品牌A":F%3===1?"品牌B":"品牌C",quantity:oe,poNo:e(h,i*5+F),deliveryNo:`DEL-2023-${3e3+i}-${F}`,status:k.status};a.push(le),k.materialItems.push(le)}),u.push(k)}),l.value==="product"?V.value=a:V.value=u,T.total=(l.value==="product"?a.length:u.length)*10,U.value=!1},500)},w=a=>{D.push({path:"/workspace/purchase/dnDetail",params:{id:a.id}})},J=a=>{console.log("确认收货",a)},Z=a=>{console.log("标记异常",a)},te=()=>{console.log("导出送货单数据")},G=()=>{console.log("打印送货单")},X=()=>{console.log("批量收货",I.value)},ae=()=>{console.log("批量退货",I.value)};return ue(()=>{o()}),(a,u)=>{const y=r("a-input"),i=r("a-select-option"),d=r("a-select"),f=r("a-range-picker"),h=r("a-form-item"),k=r("a-col"),N=r("a-button"),de=r("a-space"),F=r("a-row"),oe=r("a-form"),le=r("a-checkbox"),pe=r("a-checkbox-group"),ge=r("a-drawer"),me=r("a-button-group");return s(),v("div",Be,[R("div",Ke,[n(oe,{style:{display:"block"},layout:"inline",model:_},{default:t(()=>[n(F,null,{default:t(()=>[(s(!0),v($,null,ne(L.value,g=>(s(),x(k,{key:g.key,span:4},{default:t(()=>[n(h,{label:g.label},{default:t(()=>[g.type==="input"?(s(),x(y,{key:0,value:_[g.key],"onUpdate:value":E=>_[g.key]=E,placeholder:`请输入${g.label}`},null,8,["value","onUpdate:value","placeholder"])):g.type==="select"?(s(),x(d,{key:1,value:_[g.key],"onUpdate:value":E=>_[g.key]=E,placeholder:`请选择${g.label}`,style:{width:"100%"},allowClear:""},{default:t(()=>[(s(!0),v($,null,ne(g.options,E=>(s(),x(i,{key:E.value,value:E.value},{default:t(()=>[c(b(E.label),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value","placeholder"])):g.type==="dateRange"?(s(),x(f,{key:2,value:_[g.key],"onUpdate:value":E=>_[g.key]=E,format:"YYYY-MM-DD",style:{width:"100%"}},null,8,["value","onUpdate:value"])):M("",!0)]),_:2},1032,["label"])]),_:2},1024))),128)),n(k,{span:4,class:"search-buttons"},{default:t(()=>[n(de,null,{default:t(()=>[n(N,{type:"primary",onClick:j},{default:t(()=>u[3]||(u[3]=[c("查询")])),_:1}),n(N,{onClick:K},{default:t(()=>u[4]||(u[4]=[c("重置")])),_:1}),n(N,{type:"link",onClick:S},{default:t(()=>[n(ee(re)),u[5]||(u[5]=c(" 配置搜索项 "))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),n(ge,{title:"配置搜索项",placement:"right",visible:z.value,onClose:S,width:"400px"},{default:t(()=>[n(pe,{value:A.value,"onUpdate:value":u[0]||(u[0]=g=>A.value=g),onChange:H},{default:t(()=>[n(F,null,{default:t(()=>[(s(),v($,null,ne(O,g=>n(k,{span:12,key:g.key},{default:t(()=>[n(le,{value:g.key},{default:t(()=>[c(b(g.label),1)]),_:2},1032,["value"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"])]),_:1},8,["visible"])]),R("div",Ve,[n(me,null,{default:t(()=>[n(N,{type:l.value==="order"?"primary":"default",onClick:u[1]||(u[1]=g=>q("order"))},{default:t(()=>[n(ee(he)),u[6]||(u[6]=c(" 单据视图 "))]),_:1},8,["type"]),n(N,{type:l.value==="product"?"primary":"default",onClick:u[2]||(u[2]=g=>q("product"))},{default:t(()=>[n(ee(_e)),u[7]||(u[7]=c(" 物料视图 "))]),_:1},8,["type"])]),_:1})]),l.value==="product"?(s(),x(xe,{key:0,tableData:V.value,loading:U.value,pagination:T,onTableChange:m,onSelectChange:Y,onExport:te,onPrint:G,onViewDetail:w,onEdit:J,onCancel:Z,onColumnsChange:Q},null,8,["tableData","loading","pagination"])):(s(),x(Ee,{key:1,tableData:V.value,loading:U.value,pagination:T,onTableChange:m,onSelectChange:Y,onViewDetail:w,onEdit:J,onCancel:Z,onColumnsChange:W,onBatchReceipt:X,onBatchReturn:ae},null,8,["tableData","loading","pagination"]))])}}},Le=ie(Ue,[["__scopeId","data-v-4086ac83"]]);export{Le as default};
