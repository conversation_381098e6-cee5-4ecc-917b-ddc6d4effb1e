import{r as O,p as se,c as g,m as ne,n as N,b as w,g as t,w as e,h as d,u as re,o as u,j as n,t as o,f as p,k as y,F as q,i as A,d as v}from"./index-B4CbS3Hl.js";import{_ as de}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ue={class:"delivery-detail-container"},ce={class:"logistics-card"},pe={class:"logistics-time"},_e={class:"logistics-info"},me={class:"logistics-location"},ye={class:"log-user"},fe={class:"log-time"},ve={key:0,class:"log-detail"},ge={__name:"dnDetail",setup(ke){const $=se(),F=re(),z=O($.params.id||$.query.id),S=O(!1),i=O({id:"",deliveryNo:"DN2023121211110001",poNo:"PO-2023-0001",supplierName:"ABC供应商",status:"shipped",createTime:"2023-10-20 10:00:00",actualShippingDate:"2023-10-21 15:30:00",receiverName:"李四",receiverPhone:"13912345678",receiverAddress:"上海市浦东新区XX路XX号",receivingTimeRequirement:"工作日 9:00-17:00",remarks:"请注意轻放，内含易碎品。",items:[{id:"1",key:"1",materialName:"伺服电机",model:"SM2000",brand:"ABB",quantity:2,poNo:"SO20231212141111",status:"pending_receipt"},{id:"2",key:"2",materialName:"工业控制器",model:"IC5000",brand:"Siemens",quantity:1,poNo:"SO20231212141111",status:"fully_received"}],logisticsRecords:[{id:"log-1",provider:"顺丰速运",trackingNo:"SF1234567890",shippingTime:"2023-10-21 15:30:00",estimatedDelivery:"2023-10-23",contents:"伺服电机 x2, 工业控制器 x1",trackingInfo:[{time:"2023-10-21 15:30:00",status:"shipped",description:"已揽收",location:"供应商仓库"},{time:"2023-10-22 08:00:00",status:"in_transit",description:"运输中",location:"上海转运中心"}]}],attachments:[{id:"att-1",name:"发货单扫描件.pdf",type:"pdf",size:"1.2MB",uploadTime:"2023-10-20 11:00:00",uploadUser:"王五"}],operationLogs:[{id:"log-op-1",userName:"王五",time:"2023-10-20 10:00:00",action:"创建送货单"},{id:"log-op-2",userName:"王五",time:"2023-10-21 15:35:00",action:"更新物流信息，已发货"}],relatedPurchaseOrders:[{id:"po-1",orderNo:"PO-2023-0001",creationDate:"2023-10-15",totalAmount:5e3,status:"已发货部分"}],relatedReturnOrders:[],stepTimes:{creation:"2023-10-20 10:00:00",shipping:"2023-10-21 15:30:00",reception:null}}),L=g(()=>{var a,l,c;return[{title:"创建送货单",time:(a=i.value.stepTimes)==null?void 0:a.creation},{title:"发货",time:(l=i.value.stepTimes)==null?void 0:l.shipping},{title:"收货",time:(c=i.value.stepTimes)==null?void 0:c.reception}]});g(()=>{const l={draft:0,pending_dispatch:1,shipped:2,partially_received:2,fully_received:3,cancelled:-1}[i.value.status];return l===void 0||l===-1?i.value.status==="fully_received"?L.value.length:-1:l});const Y=g(()=>{var a;return((a=i.value.items)==null?void 0:a.reduce((l,c)=>l+(c.quantity||0),0))||0}),V=g(()=>{var a;return((a=i.value.logisticsRecords)==null?void 0:a.length)||0}),X=[{title:"物料名称",dataIndex:"materialName",key:"materialName",width:180},{title:"型号",dataIndex:"model",key:"model",width:150},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"数量",dataIndex:"quantity",key:"quantity",width:100},{title:"采购单号",dataIndex:"poNo",key:"poNo",width:150},{title:"操作",key:"action",width:100,fixed:"right"}],H=[{title:"文件名",dataIndex:"name",key:"name"},{title:"类型",dataIndex:"type",key:"type",width:80},{title:"大小",dataIndex:"size",key:"size",width:100},{title:"上传时间",dataIndex:"uploadTime",key:"uploadTime",width:150,customRender:({text:a})=>k(a)},{title:"上传人",dataIndex:"uploadUser",key:"uploadUser",width:100},{title:"操作",key:"action",width:150,fixed:"right"}],U=[{title:"采购单号",dataIndex:"orderNo",key:"orderNo"},{title:"创建日期",dataIndex:"creationDate",key:"creationDate",customRender:({text:a})=>M(a)},{title:"总金额",dataIndex:"totalAmount",key:"totalAmount"},{title:"状态",dataIndex:"status",key:"status"},{title:"操作",key:"action",width:120}],j=[{title:"退货单号",dataIndex:"orderNo",key:"orderNo"},{title:"创建日期",dataIndex:"creationDate",key:"creationDate",customRender:({text:a})=>M(a)},{title:"退货原因",dataIndex:"reason",key:"reason"},{title:"状态",dataIndex:"status",key:"status"},{title:"操作",key:"action",width:120}];g(()=>["draft","pending_dispatch"].includes(i.value.status)),g(()=>!["fully_received","cancelled"].includes(i.value.status)),g(()=>!0);const E=()=>{F.go(-1)},B=a=>({shipped:"已发货",received:"已收货",returning:"退货中",partial_returned:"已收货（部分退货）"})[a]||a,Q=a=>({shipped:"blue",received:"green",returning:"red",partial_returned:"cyan"})[a]||"default",G=a=>({pending_receipt:"待收货",partially_received:"部分收货",fully_received:"已收货"})[a]||a,J=a=>({pending_receipt:"orange",partially_received:"blue",fully_received:"green"})[a]||"default",K=a=>({shipped:"blue",in_transit:"orange",delivered:"green",exception:"red"})[a]||"blue",M=a=>a?new Date(a).toLocaleDateString():"",k=a=>{if(!a)return"";const l=new Date(a),c=l.getFullYear(),x=(l.getMonth()+1).toString().padStart(2,"0"),R=l.getDate().toString().padStart(2,"0"),r=l.getHours().toString().padStart(2,"0"),D=l.getMinutes().toString().padStart(2,"0"),b=l.getSeconds().toString().padStart(2,"0");return`${c}-${x}-${R} ${r}:${D}:${b}`},W=a=>{N.info(`对物料 ${a.materialName} (采购单: ${a.poNo}) 进行退货操作`)},Z=async()=>{S.value=!0;try{await new Promise(a=>setTimeout(a,500)),console.log("Fetching delivery details for id:",z.value),S.value=!1}catch{N.error("获取送货单详情失败"),S.value=!1}};ne(()=>{z.value?Z():N.warn("未指定送货单ID")});const P=a=>{N.info(`查看采购单详情: ${a}`)},ee=a=>{N.info(`查看退货单详情: ${a}`)};return(a,l)=>{const c=d("a-button"),x=d("a-space"),R=d("a-page-header"),r=d("a-descriptions-item"),D=d("a-tag"),b=d("a-descriptions"),h=d("a-card"),I=d("a-table"),f=d("a-tab-pane"),te=d("a-timeline-item"),ae=d("a-timeline"),C=d("a-tabs"),T=d("a-empty"),le=d("a-list-item-meta"),ie=d("a-list-item"),oe=d("a-list");return u(),w("div",ue,[t(R,{title:"送货单详情 - "+i.value.deliveryNo,"sub-title":"状态："+B(i.value.status),onBack:E},{extra:e(()=>[t(x,null,{default:e(()=>[t(c,{type:"primary"},{default:e(()=>l[0]||(l[0]=[n("收货")])),_:1}),t(c,null,{default:e(()=>l[1]||(l[1]=[n("退货")])),_:1})]),_:1})]),_:1},8,["title","sub-title"]),t(h,{title:"基本信息",class:"detail-card"},{default:e(()=>[t(b,{bordered:"",column:2,size:"small"},{default:e(()=>[t(r,{label:"送货单号"},{default:e(()=>[n(o(i.value.deliveryNo),1)]),_:1}),t(r,{label:"状态"},{default:e(()=>[t(D,{color:Q(i.value.status)},{default:e(()=>[n(o(B(i.value.status)),1)]),_:1},8,["color"])]),_:1}),t(r,{label:"物料总数量"},{default:e(()=>[n(o(Y.value),1)]),_:1}),t(r,{label:"物流单数量"},{default:e(()=>[n(o(V.value),1)]),_:1}),t(r,{label:"创建时间"},{default:e(()=>[n(o(k(i.value.createTime)),1)]),_:1}),t(r,{label:"收货时间"},{default:e(()=>[n(o(k(i.value.stepTimes.reception)),1)]),_:1})]),_:1})]),_:1}),t(h,{title:"物料明细",class:"detail-card"},{default:e(()=>[t(I,{columns:X,"data-source":i.value.items,pagination:!1,size:"middle",scroll:{x:1200}},{bodyCell:e(({column:s,record:_})=>[s.key==="status"?(u(),p(D,{key:0,color:J(_.status)},{default:e(()=>[n(o(G(_.status)),1)]),_:2},1032,["color"])):y("",!0),s.key==="poNo"?(u(),p(c,{key:1,type:"link",size:"small",onClick:m=>P(_.poNo)},{default:e(()=>[n(o(_.poNo),1)]),_:2},1032,["onClick"])):y("",!0),s.key==="action"?(u(),p(c,{key:2,type:"link",size:"small",onClick:m=>W(_)},{default:e(()=>l[2]||(l[2]=[n("退货")])),_:2},1032,["onClick"])):y("",!0)]),_:1},8,["data-source"])]),_:1}),t(h,{title:"收货与物流信息",class:"detail-card"},{default:e(()=>[t(C,{"default-active-key":"1"},{default:e(()=>[t(f,{key:"1",tab:"收货信息"},{default:e(()=>[t(b,{bordered:"",column:2,size:"small"},{default:e(()=>[t(r,{label:"收货人"},{default:e(()=>[n(o(i.value.receiverName),1)]),_:1}),t(r,{label:"联系电话"},{default:e(()=>[n(o(i.value.receiverPhone),1)]),_:1}),t(r,{label:"收货地址",span:2},{default:e(()=>[n(o(i.value.receiverAddress),1)]),_:1}),t(r,{label:"收货要求",span:2},{default:e(()=>[n(o(i.value.receivingTimeRequirement),1)]),_:1})]),_:1})]),_:1}),i.value.logisticsRecords&&i.value.logisticsRecords.length>0?(u(),p(f,{key:"logistics-content",tab:"物流信息"},{default:e(()=>[t(C,{type:"card","tab-position":"top"},{default:e(()=>[(u(!0),w(q,null,A(i.value.logisticsRecords,(s,_)=>(u(),p(f,{key:s.id,tab:`物流单 ${_+1}`},{default:e(()=>[v("div",ce,[t(b,{bordered:"",size:"small",column:2},{default:e(()=>[t(r,{label:"物流公司"},{default:e(()=>[n(o(s.provider),1)]),_:2},1024),t(r,{label:"物流单号"},{default:e(()=>[n(o(s.trackingNo),1)]),_:2},1024),t(r,{label:"发货时间"},{default:e(()=>[n(o(k(s.shippingTime)),1)]),_:2},1024),t(r,{label:"预计送达时间"},{default:e(()=>[n(o(M(s.estimatedDelivery)),1)]),_:2},1024),t(r,{label:"物流内容",span:2},{default:e(()=>[n(o(s.contents),1)]),_:2},1024)]),_:2},1024),t(ae,{style:{"margin-top":"16px"}},{default:e(()=>[(u(!0),w(q,null,A(s.trackingInfo,m=>(u(),p(te,{key:m.time,color:K(m.status)},{default:e(()=>[v("p",pe,o(k(m.time)),1),v("p",_e,o(m.description),1),v("p",me,"当前位置: "+o(m.location),1)]),_:2},1032,["color"]))),128))]),_:2},1024)])]),_:2},1032,["tab"]))),128))]),_:1})]),_:1})):(u(),p(f,{key:"logistics-empty",tab:"物流信息"},{default:e(()=>[t(T,{description:"暂无物流信息"})]),_:1}))]),_:1})]),_:1}),t(h,{title:"相关单据",class:"detail-card"},{default:e(()=>[t(C,{"default-active-key":"po"},{default:e(()=>[t(f,{key:"po",tab:"采购单"},{default:e(()=>[t(I,{columns:U,"data-source":i.value.relatedPurchaseOrders,pagination:!1,size:"small"},{bodyCell:e(({column:s,record:_})=>[s.key==="action"?(u(),p(c,{key:0,type:"link",onClick:m=>P(_.id)},{default:e(()=>l[3]||(l[3]=[n("查看详情")])),_:2},1032,["onClick"])):y("",!0)]),_:1},8,["data-source"]),!i.value.relatedPurchaseOrders||i.value.relatedPurchaseOrders.length===0?(u(),p(T,{key:0,description:"暂无关联采购单"})):y("",!0)]),_:1}),t(f,{key:"returnOrder",tab:"退货单"},{default:e(()=>[t(I,{columns:j,"data-source":i.value.relatedReturnOrders,pagination:!1,size:"small"},{bodyCell:e(({column:s,record:_})=>[s.key==="action"?(u(),p(c,{key:0,type:"link",onClick:m=>ee(_.id)},{default:e(()=>l[4]||(l[4]=[n("查看详情")])),_:2},1032,["onClick"])):y("",!0)]),_:1},8,["data-source"]),!i.value.relatedReturnOrders||i.value.relatedReturnOrders.length===0?(u(),p(T,{key:0,description:"暂无退货单"})):y("",!0)]),_:1})]),_:1})]),_:1}),t(h,{title:"其他信息",class:"detail-card"},{default:e(()=>[t(C,{"default-active-key":"1"},{default:e(()=>[t(f,{key:"1",tab:"附件资料"},{default:e(()=>[t(I,{columns:H,"data-source":i.value.attachments,pagination:!1,size:"small",style:{"margin-top":"16px"}},{bodyCell:e(({column:s})=>[s.key==="action"?(u(),p(x,{key:0},{default:e(()=>l[5]||(l[5]=[v("a",null,"预览",-1),v("a",null,"下载",-1)])),_:1})):y("",!0)]),_:1},8,["data-source"])]),_:1}),t(f,{key:"3",tab:"操作历史"},{default:e(()=>[t(oe,{"item-layout":"horizontal","data-source":i.value.operationLogs,size:"small"},{renderItem:e(({item:s})=>[t(ie,null,{default:e(()=>[t(le,null,{title:e(()=>[v("span",ye,o(s.userName),1),n(" "+o(s.action),1)]),description:e(()=>[v("div",fe,o(k(s.time)),1),s.detail?(u(),w("div",ve,o(s.detail),1)):y("",!0)]),_:2},1024)]),_:2},1024)]),_:1},8,["data-source"])]),_:1})]),_:1})]),_:1})])}}},Ne=de(ge,[["__scopeId","data-v-acf34ae5"]]);export{Ne as default};
