import{r as T,c as rt,b as k,d as u,g as e,w as t,h as i,j as n,l as A,G as ht,t as f,f as b,k as v,F as D,i as ut,u as kt,o as r,D as St,p as It,a as vt,m as Tt}from"./index-B4CbS3Hl.js";import{_ as gt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{E as xt,P as bt}from"./PrinterOutlined-BB5W7ImW.js";import{S as yt}from"./SettingOutlined-DSrSqAUc.js";import{O as Dt,A as $t}from"./OrderedListOutlined-UAwTKBXr.js";const Qt={class:"table-area"},Ft={class:"table-operations"},At={class:"selection-summary"},Mt={style:{color:"#666"}},Pt={class:"summary-content"},Nt=["onClick"],Ot=["onClick"],Rt=["onClick"],Et={__name:"materialTable",props:{tableData:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},pagination:{type:Object,default:()=>({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0})}},emits:["tableChange","selectChange","export","print","viewDetail","edit","cancel","delete","columnsChange"],setup(O,{emit:W}){const nt=kt(),R=T(!1),E=()=>{R.value=!R.value},X=[{title:"物料名称",dataIndex:"name",key:"name",width:180,fixed:"left"},{title:"型号",dataIndex:"model",key:"model",width:150,fixed:"left"},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"分类",dataIndex:"category",key:"category",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"备货中数量",dataIndex:"preparingQuantity",key:"preparingQuantity",width:110},{title:"在途数量",dataIndex:"inTransitQuantity",key:"inTransitQuantity",width:100},{title:"已验收数量",dataIndex:"acceptedQuantity",key:"acceptedQuantity",width:110},{title:"已取消数量",dataIndex:"cancelledQuantity",key:"cancelledQuantity",width:110},{title:"物流状态",dataIndex:"logisticsStatus",key:"logisticsStatus",width:100},{title:"财务状态",dataIndex:"financialStatus",key:"financialStatus",width:100},{title:"单价（¥）",dataIndex:"unitPrice",key:"unitPrice",width:100},{title:"总价（¥）",dataIndex:"totalPrice",key:"totalPrice",width:120},{title:"来源询价单",dataIndex:"rfqNo",key:"rfqNo",width:150},{title:"订单号",dataIndex:"soNo",key:"soNo",width:200},{title:"订单状态",dataIndex:"status",key:"status",width:100},{title:"采购员",dataIndex:"creator",key:"creator",width:100},{title:"下单时间",dataIndex:"orderTime",key:"orderTime",width:150},{title:"预计到货日期",dataIndex:"expectedArrivalTime",key:"expectedArrivalTime",width:150},{title:"备注",dataIndex:"remark",key:"remark",width:150},{title:"操作（⚠️详见需求）",dataIndex:"action",key:"action",width:180,fixed:"right"}],M=T(["soNo","name","model","quantity","preparingQuantity","inTransitQuantity","acceptedQuantity","cancelledQuantity","logisticsStatus","financialStatus","unitPrice","totalPrice","expectedArrivalTime","action"]),C=rt(()=>X.filter(d=>M.value.includes(d.dataIndex)||d.fixed)),j=d=>{M.value=d,w("columnsChange",d)},J=O,w=W,P=T([]),q=rt(()=>parseFloat(J.tableData.filter(d=>P.value.includes(d.id)).reduce((d,c)=>d+(c.totalPrice||0),0).toFixed(2))),U=d=>{P.value=d,w("selectChange",d)},N=d=>{w("tableChange",d)},V=d=>({not_submitted:"default",pending_confirmation:"orange",in_progress:"blue",completed:"green",cancelling:"purple",cancelled:"red"})[d]||"default",L=d=>({not_submitted:"草稿",pending_confirmation:"待确认",in_progress:"执行中",completed:"已完成",cancelling:"取消中",cancelled:"已取消"})[d]||"未知",lt=d=>({备货中:"cyan",待发货:"blue",部分发货:"purple",部分收货:"orange",退货中:"magenta",已收货:"green"})[d]||"default",ct=d=>({未对账:"red",部分对账:"yellow",部分付款:"lime",已支付:"green"})[d]||"default",pt=d=>{nt.push({path:"/workspace/purchase/poDetail",query:{id:d.id}})},Z=d=>{w("edit",d)},G=d=>{w("cancel",d)},ot=d=>{w("delete",d)},it=()=>{w("export")},st=()=>{w("print")};return(d,c)=>{const H=i("a-button"),o=i("a-space"),y=i("a-tooltip"),$=i("a-tag"),a=i("a-popconfirm"),l=i("a-table"),p=i("a-checkbox"),z=i("a-col"),Q=i("a-row"),F=i("a-checkbox-group"),B=i("a-drawer");return r(),k("div",Qt,[u("div",Ft,[e(o,null,{default:t(()=>[e(H,{type:"primary",onClick:it},{default:t(()=>[e(A(xt)),c[1]||(c[1]=n(" 导出 "))]),_:1}),e(H,{type:"primary",onClick:st},{default:t(()=>[e(A(bt)),c[2]||(c[2]=n(" 打印 "))]),_:1})]),_:1}),e(H,{onClick:E},{default:t(()=>[e(A(yt)),c[3]||(c[3]=n(" 列设置 "))]),_:1})]),u("div",At,[u("div",null,[e(y,{placement:"top"},{title:t(()=>c[4]||(c[4]=[u("div",null,"1. 本表中的价格若未做特殊说明，均为含税价格。",-1),u("div",null,[n("2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下："),u("br"),n("订单总金额¥0.00 - ¥499.99，运费¥15.00"),u("br"),n("订单总金额¥500.00 - ¥999.99，运费¥8.00"),u("br"),n("订单总金额¥1000以上，免运费")],-1)])),default:t(()=>[u("span",Mt,[e(A(ht),{style:{"margin-right":"4px"}}),c[5]||(c[5]=n("价格与运费说明"))])]),_:1})]),u("div",Pt,[u("span",null,[c[6]||(c[6]=n("已选择：")),e($,{color:"red"},{default:t(()=>[n(f(P.value.length),1)]),_:1}),c[7]||(c[7]=n(" 个物料"))]),u("span",null,[c[8]||(c[8]=n("总金额：")),e($,{color:"red"},{default:t(()=>[n("¥"+f(q.value.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]),_:1})])])]),e(l,{columns:C.value,"data-source":O.tableData,loading:O.loading,pagination:O.pagination,onChange:N,"row-key":"id","row-selection":{selectedRowKeys:P.value,onChange:U},bordered:"",scroll:{x:1500}},{bodyCell:t(({column:s,record:g})=>[s.dataIndex==="status"?(r(),b($,{key:0,color:V(g.status)},{default:t(()=>[n(f(L(g.status)),1)]),_:2},1032,["color"])):v("",!0),s.dataIndex==="unitPrice"||s.dataIndex==="totalPrice"||s.dataIndex==="totalAmount"?(r(),k(D,{key:1},[n(f(parseFloat(g[s.dataIndex]).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)],64)):v("",!0),s.dataIndex==="preparingQuantity"||s.dataIndex==="inTransitQuantity"||s.dataIndex==="acceptedQuantity"||s.dataIndex==="cancelledQuantity"?(r(),k(D,{key:2},[n(f(g[s.dataIndex]||0),1)],64)):v("",!0),s.dataIndex==="expectedArrivalTime"?(r(),k(D,{key:3},[n(f(g.expectedArrivalTime?g.expectedArrivalTime:"-"),1)],64)):v("",!0),s.dataIndex==="logisticsStatus"?(r(),b($,{key:4,color:lt(g.logisticsStatus)},{default:t(()=>[n(f(g.logisticsStatus||"-"),1)]),_:2},1032,["color"])):v("",!0),s.dataIndex==="financialStatus"?(r(),b($,{key:5,color:ct(g.financialStatus)},{default:t(()=>[n(f(g.financialStatus||"-"),1)]),_:2},1032,["color"])):v("",!0),s.dataIndex==="action"?(r(),b(o,{key:6},{default:t(()=>[u("a",{onClick:K=>pt(g)},"订单详情",8,Nt),["not_submitted"].includes(g.status)?(r(),k("a",{key:0,onClick:K=>Z(g)},"编辑",8,Ot)):v("",!0),["pending_confirmation","in_progress"].includes(g.status)?(r(),k("a",{key:1,onClick:K=>G(g),class:"danger-link"},"取消",8,Rt)):v("",!0),g.status==="not_submitted"?(r(),b(a,{key:2,title:"确定要删除此物料吗?","ok-text":"确定","cancel-text":"取消",onConfirm:K=>ot(g)},{default:t(()=>c[9]||(c[9]=[u("a",{class:"danger-link"},"删除",-1)])),_:2},1032,["onConfirm"])):v("",!0)]),_:2},1024)):v("",!0)]),_:1},8,["columns","data-source","loading","pagination","row-selection"]),e(B,{title:"配置表格列",placement:"right",visible:R.value,onClose:E,width:"400px"},{default:t(()=>[e(F,{value:M.value,"onUpdate:value":c[0]||(c[0]=s=>M.value=s),onChange:j},{default:t(()=>[e(Q,null,{default:t(()=>[(r(),k(D,null,ut(X,s=>e(z,{span:12,key:s.dataIndex},{default:t(()=>[e(p,{value:s.dataIndex,disabled:s.fixed},{default:t(()=>[n(f(s.title),1)]),_:2},1032,["value","disabled"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"])]),_:1},8,["visible"])])}}},qt=gt(Et,[["__scopeId","data-v-bc3c1439"]]),Ut={class:"table-area"},Lt={class:"table-operations"},zt={class:"selection-summary"},Bt={style:{color:"#666"}},Vt={class:"summary-content"},Kt={style:{margin:"12px"}},Yt=["onClick"],jt=["onClick"],Jt={__name:"orderTable",props:{tableData:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},pagination:{type:Object,default:()=>({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0})}},emits:["tableChange","selectChange","export","print","viewDetail","edit","cancel","delete","columnsChange"],setup(O,{emit:W}){const nt=kt();It();const R=T(!1),E=()=>{R.value=!R.value},X=[{title:"订单号",dataIndex:"soNo",key:"soNo",width:180,fixed:"left"},{title:"订单状态",dataIndex:"status",key:"status",width:100},{title:"来源询价单",dataIndex:"rfqNo",key:"rfqNo",width:180},{title:"物料数量",dataIndex:"materialCount",key:"materialCount",width:90},{title:"总金额（¥）",dataIndex:"totalAmount",key:"totalAmount",width:120},{title:"采购员",dataIndex:"creator",key:"creator",width:100},{title:"下单时间",dataIndex:"createTime",key:"createTime",width:150},{title:"结束时间",dataIndex:"endTime",key:"endTime",width:150},{title:"操作（⚠️看需求）",dataIndex:"action",key:"action",width:180,fixed:"right"}],M=T(["soNo","status","materialCount","totalAmount","creator","createTime","endTime","action"]),C=rt(()=>X.filter(a=>M.value.includes(a.dataIndex)||a.fixed)),j=[{title:"物料名称",dataIndex:"name",key:"name",width:180},{title:"型号",dataIndex:"model",key:"model",width:150},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"分类",dataIndex:"category",key:"category",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"已发货数量",dataIndex:"shippedQuantity",key:"shippedQuantity",width:110},{title:"已收货数量",dataIndex:"receivedQuantity",key:"receivedQuantity",width:110},{title:"已取消数量",dataIndex:"cancelledQuantity",key:"cancelledQuantity",width:110},{title:"物流状态",dataIndex:"logisticsStatus",key:"logisticsStatus",width:100},{title:"财务状态",dataIndex:"financialStatus",key:"financialStatus",width:100},{title:"单价（¥）",dataIndex:"unitPrice",key:"unitPrice",width:100},{title:"总价（¥）",dataIndex:"totalPrice",key:"totalPrice",width:120},{title:"预计到货时间",dataIndex:"expectedArrivalTime",key:"expectedArrivalTime",width:150},{title:"操作（⚠️看需求）",dataIndex:"action",key:"action",width:180,fixed:"right"}],J=T(j.map(a=>a.dataIndex)),w=rt(()=>j.filter(a=>J.value.includes(a.dataIndex)||a.fixed)),P=a=>{M.value=a},q=a=>{J.value=a},U=O,N=W,V=T([]),L=T([]),lt=(a,l)=>{a?L.value=[...L.value,l.id]:L.value=L.value.filter(p=>p!==l.id)},ct=rt(()=>parseFloat(U.tableData.filter(a=>V.value.includes(a.id)).reduce((a,l)=>a+(l.totalAmount||0),0).toFixed(2))),pt=a=>{V.value=a,N("selectChange",a)},Z=({key:a})=>{console.log(`Batch operation triggered: ${a}`,V.value)},G=a=>{N("tableChange",a)},ot=a=>({not_submitted:"default",pending_confirmation:"orange",in_progress:"blue",completed:"green",cancelling:"purple",cancelled:"red"})[a]||"default",it=a=>({not_submitted:"草稿",pending_confirmation:"待确认",in_progress:"执行中",completed:"已完成",cancelling:"取消中",cancelled:"已取消"})[a]||"未知",st=a=>({备货中:"cyan",待发货:"blue",部分发货:"purple",部分收货:"orange",退货中:"magenta",已收货:"green"})[a]||"default",d=a=>({未对账:"red",部分对账:"yellow",部分付款:"lime",已支付:"green"})[a]||"default",c=a=>{nt.push({path:"/workspace/purchase/poDetail",query:{id:a.id}})},H=a=>{N("cancel",a)},o=a=>{N("delete",a)},y=()=>{N("export")},$=()=>{N("print")};return(a,l)=>{const p=i("a-button"),z=i("a-menu-item"),Q=i("a-menu"),F=i("a-dropdown"),B=i("a-space"),s=i("a-tooltip"),g=i("a-tag"),K=i("a-table"),I=i("a-popconfirm"),S=i("a-checkbox"),tt=i("a-col"),m=i("a-row"),x=i("a-checkbox-group"),et=i("a-divider"),dt=i("a-drawer");return r(),k("div",Ut,[u("div",Lt,[e(B,null,{default:t(()=>[e(F,null,{overlay:t(()=>[e(Q,{onClick:Z},{default:t(()=>[e(z,{key:"invoice"},{default:t(()=>l[3]||(l[3]=[n("开票")])),_:1}),e(z,{key:"pay"},{default:t(()=>l[4]||(l[4]=[n("付款")])),_:1}),e(z,{key:"archive"},{default:t(()=>l[5]||(l[5]=[n("归档")])),_:1})]),_:1})]),default:t(()=>[e(p,{type:"primary"},{default:t(()=>[l[2]||(l[2]=n(" 批量操作 ")),e(A(St))]),_:1})]),_:1}),e(p,{type:"primary",onClick:y},{default:t(()=>[e(A(xt)),l[6]||(l[6]=n(" 导出 "))]),_:1}),e(p,{type:"primary",onClick:$},{default:t(()=>[e(A(bt)),l[7]||(l[7]=n(" 打印 "))]),_:1})]),_:1}),e(p,{onClick:E},{default:t(()=>[e(A(yt)),l[8]||(l[8]=n(" 列设置 "))]),_:1})]),u("div",zt,[u("div",null,[e(s,{placement:"top"},{title:t(()=>l[9]||(l[9]=[u("div",null,"1. 本表中的价格若未做特殊说明，均为含税价格。",-1),u("div",null,[n("2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下："),u("br"),n("订单总金额¥0.00 - ¥499.99，运费¥15.00"),u("br"),n("订单总金额¥500.00 - ¥999.99，运费¥8.00"),u("br"),n("订单总金额¥1000以上，免运费")],-1)])),default:t(()=>[u("span",Bt,[e(A(ht),{style:{"margin-right":"4px"}}),l[10]||(l[10]=n("价格与运费说明"))])]),_:1})]),u("div",Vt,[u("span",null,[l[11]||(l[11]=n("已选择：")),e(g,{color:"red"},{default:t(()=>[n(f(V.value.length),1)]),_:1}),l[12]||(l[12]=n(" 个订单"))]),u("span",null,[l[13]||(l[13]=n("总金额：")),e(g,{color:"red"},{default:t(()=>[n("¥"+f(ct.value.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)]),_:1})])])]),e(K,{columns:C.value,"data-source":O.tableData,loading:O.loading,pagination:O.pagination,onChange:G,"row-key":"id",size:"small","row-selection":{selectedRowKeys:V.value,onChange:pt},bordered:"",scroll:{x:1500},expandable:{expandedRowKeys:L.value,onExpand:lt,expandRowByClick:!0}},{expandedRowRender:t(({record:_})=>[u("div",Kt,[e(K,{columns:w.value,"data-source":_.materialItems,pagination:!1,"row-key":"id",bordered:"",size:"small"},{bodyCell:t(({column:h,text:at,record:Y})=>[h.dataIndex==="unitPrice"||h.dataIndex==="totalPrice"?(r(),k(D,{key:0},[n(f(parseFloat(at).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)],64)):v("",!0),h.dataIndex==="shippedQuantity"||h.dataIndex==="receivedQuantity"||h.dataIndex==="cancelledQuantity"?(r(),k(D,{key:1},[n(f(at||0),1)],64)):v("",!0),h.dataIndex==="expectedArrivalTime"?(r(),k(D,{key:2},[n(f(Y.expectedArrivalTime?Y.expectedArrivalTime:"-"),1)],64)):v("",!0),h.dataIndex==="logisticsStatus"?(r(),b(g,{key:3,color:st(Y.logisticsStatus)},{default:t(()=>[n(f(Y.logisticsStatus||"-"),1)]),_:2},1032,["color"])):v("",!0),h.dataIndex==="financialStatus"?(r(),b(g,{key:4,color:d(Y.financialStatus)},{default:t(()=>[n(f(Y.financialStatus||"-"),1)]),_:2},1032,["color"])):v("",!0)]),_:2},1032,["columns","data-source"])])]),bodyCell:t(({column:_,record:h})=>[_.dataIndex==="status"?(r(),b(g,{key:0,color:ot(h.status)},{default:t(()=>[n(f(it(h.status)),1)]),_:2},1032,["color"])):v("",!0),_.dataIndex==="totalAmount"?(r(),k(D,{key:1},[n(f(parseFloat(h.totalAmount).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})),1)],64)):v("",!0),_.dataIndex==="action"?(r(),b(B,{key:2},{default:t(()=>[u("a",{onClick:at=>c(h)},"详情",8,Yt),["pending_confirmation","in_progress"].includes(h.status)?(r(),k("a",{key:0,onClick:at=>H(h),class:"danger-link"},"取消",8,jt)):v("",!0),h.status==="not_submitted"?(r(),b(I,{key:1,title:"确定要删除此订单吗?","ok-text":"确定","cancel-text":"取消",onConfirm:at=>o(h)},{default:t(()=>l[14]||(l[14]=[u("a",{class:"danger-link"},"删除",-1)])),_:2},1032,["onConfirm"])):v("",!0)]),_:2},1024)):v("",!0)]),_:1},8,["columns","data-source","loading","pagination","row-selection","expandable"]),e(dt,{title:"配置表格列",placement:"right",visible:R.value,onClose:E,width:"400px"},{default:t(()=>[l[15]||(l[15]=u("h4",{style:{"margin-bottom":"12px"}},"订单列配置",-1)),e(x,{value:M.value,"onUpdate:value":l[0]||(l[0]=_=>M.value=_),onChange:P},{default:t(()=>[e(m,null,{default:t(()=>[(r(),k(D,null,ut(X,_=>e(tt,{span:12,key:_.dataIndex},{default:t(()=>[e(S,{value:_.dataIndex,disabled:_.fixed},{default:t(()=>[n(f(_.title),1)]),_:2},1032,["value","disabled"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"]),e(et),l[16]||(l[16]=u("h4",{style:{"margin-top":"24px","margin-bottom":"12px"}},"物料列配置 (展开行内)",-1)),e(x,{value:J.value,"onUpdate:value":l[1]||(l[1]=_=>J.value=_),onChange:q},{default:t(()=>[e(m,null,{default:t(()=>[(r(),k(D,null,ut(j,_=>e(tt,{span:12,key:_.dataIndex},{default:t(()=>[e(S,{value:_.dataIndex,disabled:_.fixed},{default:t(()=>[n(f(_.title),1)]),_:2},1032,["value","disabled"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"])]),_:1},8,["visible"])])}}},Gt=gt(Jt,[["__scopeId","data-v-382200e8"]]),Ht={class:"po-container"},Wt={class:"search-area"},Xt={class:"view-selector"},Zt={__name:"index",setup(O){const W=T(!1),nt=()=>{W.value=!W.value},R=[{key:"soNo",label:"采购订单号",type:"input"},{key:"materialName",label:"物料名称",type:"input"},{key:"materialModel",label:"物料型号",type:"input"},{key:"createTimeRange",label:"下单时间",type:"dateRange"},{key:"status",label:"订单状态",type:"select",options:[{label:"未提交",value:"not_submitted"},{label:"待确认",value:"pending_confirmation"},{label:"执行中",value:"in_progress"},{label:"已完成",value:"completed"},{label:"取消中",value:"cancelling"},{label:"已取消",value:"cancelled"}]},{key:"creator",label:"采购员",type:"input"},{key:"category",label:"物料分类",type:"select",options:[{label:"电子元件",value:"electronics"},{label:"机械零件",value:"mechanical"},{label:"原材料",value:"raw"}]},{key:"brand",label:"品牌",type:"input"}],E=T(["soNo","status","createTimeRange"]),X=rt(()=>R.filter(o=>E.value.includes(o.key))),M=o=>{E.value=o},C=vt({soNo:"",materialName:"",materialModel:"",createTimeRange:[],status:void 0,creator:"",category:void 0,brand:""}),j=()=>{q.current=1,G()},J=()=>{Object.keys(C).forEach(o=>{Array.isArray(C[o])?C[o]=[]:C[o]=void 0}),j()},w=T([]),P=T(!1),q=vt({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0}),U=T("order"),N=o=>{q.current=o.current,q.pageSize=o.pageSize,G()},V=T([]),L=o=>{V.value=o},lt=o=>{U.value=o,G()},ct=o=>{console.log("Material columns changed:",o)},pt=o=>{console.log("Order columns changed:",o)},Z=o=>{const y=o.getFullYear(),$=(o.getMonth()+1).toString().padStart(2,"0"),a=o.getDate().toString().padStart(2,"0"),l=o.getHours().toString().padStart(2,"0"),p=o.getMinutes().toString().padStart(2,"0");return`${y}-${$}-${a} ${l}:${p}`},G=()=>{P.value=!0,setTimeout(()=>{const o=[],y=[];Array.from({length:10}).forEach(($,a)=>{const l=["not_submitted","pending_confirmation","in_progress","completed","cancelling","cancelled"],p=l[a%l.length];let z="non_invoiceable",Q="not_applicable";const F=new Date(Date.now()-Math.random()*10*24*60*60*1e3);let B=null;["in_progress","completed"].includes(p)&&(z=["invoiceable","invoicing","invoiced"][a%3]),p==="completed"?Q="paid":p==="in_progress"?Q=["pending_payment","partially_paid"][a%2]:p==="cancelled"&&(Q=["refunded","partially_refunded"][a%2]),["completed","cancelled"].includes(p)&&(B=new Date(F.getTime()+Math.random()*5*24*60*60*1e3));const s={id:`order-${a}`,soNo:p==="not_submitted"?`SO-DRAFT-${2e3+a}`:`SO-2023-${2e3+a}`,rfqNo:`RFQ-2023-${2e3+a}`,status:p,invoicingStatus:z,paymentStatus:Q,creator:`管理员${a%3+1}`,orderTime:"2023-07-01",createTime:p==="not_submitted"?null:Z(F),endTime:B?Z(B):null,actualDelivery:p==="completed"?"2023-08-13":null,totalAmount:0,materialCount:0,materialItems:[]},g=Math.floor(Math.random()*5)+1;s.materialCount=0,Array.from({length:g}).forEach((K,I)=>{const S=Math.floor(Math.random()*100)+1,tt=parseFloat((Math.random()*1e3+100).toFixed(2)),m=parseFloat((S*tt).toFixed(2));s.totalAmount+=m,s.materialCount+=S;let x=0,et=0,dt=0,_=0;const h=["备货中","待发货","部分发货","部分收货","退货中","已收货"],at=h[I%h.length],Y=["未对账","部分对账","部分付款","已支付"],Ct=Y[I%Y.length];if(p==="in_progress"){const mt=S;x=Math.floor(mt*.4),et=Math.floor(mt*.3),dt=mt-x-et}else p==="completed"?dt=S:p==="cancelling"?(_=Math.floor(S*.7),x=Math.floor(S*.1),et=Math.floor(S*.1),dt=S-_-x-et):p==="cancelled"&&(_=S);const _t=["in_progress","cancelling"].includes(p)?new Date(Date.now()+Math.random()*10*24*60*60*1e3):null,wt=_t?Z(_t):null,ft={id:`material-${a}-${I}`,soNo:s.soNo,name:`测试物料 ${I+1}`,model:`MODEL-${1e3+I}`,brand:I%3===0?"品牌A":I%3===1?"品牌B":"品牌C",category:I%2===0?"电子元件":"机械零件",unit:"个",quantity:S,unitPrice:tt,totalPrice:m,rfqNo:`RFQ-2023-${2e3+I}`,status:p,preparingQuantity:x,inTransitQuantity:et,acceptedQuantity:dt,cancelledQuantity:_,creator:s.creator,orderTime:s.orderTime,expectedArrivalTime:wt,actualDelivery:s.actualDelivery,remark:I%2===0?"特殊规格":"",logisticsStatus:at,financialStatus:Ct};o.push(ft),s.materialItems.push(ft)}),s.totalAmount=parseFloat(s.totalAmount.toFixed(2)),y.push(s)}),U.value==="product"?w.value=o:w.value=y,q.total=(U.value==="product"?o.length:y.length)*10,P.value=!1},500)},ot=o=>{console.log("查看采购单详情",o)},it=o=>{console.log("编辑采购单",o)},st=o=>{console.log("取消采购单",o)},d=o=>{console.log("删除采购单",o)},c=()=>{console.log("导出采购单数据")},H=()=>{console.log("打印采购单")};return Tt(()=>{G()}),(o,y)=>{const $=i("a-input"),a=i("a-select-option"),l=i("a-select"),p=i("a-range-picker"),z=i("a-form-item"),Q=i("a-col"),F=i("a-button"),B=i("a-space"),s=i("a-row"),g=i("a-form"),K=i("a-checkbox"),I=i("a-checkbox-group"),S=i("a-drawer"),tt=i("a-button-group");return r(),k("div",Ht,[u("div",Wt,[e(g,{style:{display:"block"},layout:"inline",model:C},{default:t(()=>[e(s,null,{default:t(()=>[(r(!0),k(D,null,ut(X.value,m=>(r(),b(Q,{key:m.key,span:4},{default:t(()=>[e(z,{label:m.label},{default:t(()=>[m.type==="input"?(r(),b($,{key:0,value:C[m.key],"onUpdate:value":x=>C[m.key]=x,placeholder:`请输入${m.label}`},null,8,["value","onUpdate:value","placeholder"])):m.type==="select"?(r(),b(l,{key:1,value:C[m.key],"onUpdate:value":x=>C[m.key]=x,placeholder:`请选择${m.label}`,style:{width:"100%"},allowClear:""},{default:t(()=>[(r(!0),k(D,null,ut(m.options,x=>(r(),b(a,{key:x.value,value:x.value},{default:t(()=>[n(f(x.label),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value","placeholder"])):m.type==="dateRange"?(r(),b(p,{key:2,value:C[m.key],"onUpdate:value":x=>C[m.key]=x,format:"YYYY-MM-DD",style:{width:"100%"}},null,8,["value","onUpdate:value"])):v("",!0)]),_:2},1032,["label"])]),_:2},1024))),128)),e(Q,{span:4,class:"search-buttons"},{default:t(()=>[e(B,null,{default:t(()=>[e(F,{type:"primary",onClick:j},{default:t(()=>y[3]||(y[3]=[n("查询")])),_:1}),e(F,{onClick:J},{default:t(()=>y[4]||(y[4]=[n("重置")])),_:1}),e(F,{type:"link",onClick:nt},{default:t(()=>[e(A(yt)),y[5]||(y[5]=n(" 配置搜索项 "))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),e(S,{title:"配置搜索项",placement:"right",visible:W.value,onClose:nt,width:"400px"},{default:t(()=>[e(I,{value:E.value,"onUpdate:value":y[0]||(y[0]=m=>E.value=m),onChange:M},{default:t(()=>[e(s,null,{default:t(()=>[(r(),k(D,null,ut(R,m=>e(Q,{span:12,key:m.key},{default:t(()=>[e(K,{value:m.key},{default:t(()=>[n(f(m.label),1)]),_:2},1032,["value"])]),_:2},1024)),64))]),_:1})]),_:1},8,["value"])]),_:1},8,["visible"])]),u("div",Xt,[e(tt,null,{default:t(()=>[e(F,{type:U.value==="order"?"primary":"default",onClick:y[1]||(y[1]=m=>lt("order"))},{default:t(()=>[e(A(Dt)),y[6]||(y[6]=n(" 订单视图 "))]),_:1},8,["type"]),e(F,{type:U.value==="product"?"primary":"default",onClick:y[2]||(y[2]=m=>lt("product"))},{default:t(()=>[e(A($t)),y[7]||(y[7]=n(" 物料视图 "))]),_:1},8,["type"])]),_:1})]),U.value==="product"?(r(),b(qt,{key:0,tableData:w.value,loading:P.value,pagination:q,onTableChange:N,onSelectChange:L,onExport:c,onPrint:H,onViewDetail:ot,onEdit:it,onCancel:st,onDelete:d,onColumnsChange:ct},null,8,["tableData","loading","pagination"])):(r(),b(Gt,{key:1,tableData:w.value,loading:P.value,pagination:q,onTableChange:N,onSelectChange:L,onExport:c,onPrint:H,onViewDetail:ot,onEdit:it,onCancel:st,onDelete:d,onColumnsChange:pt},null,8,["tableData","loading","pagination"]))])}}},oe=gt(Zt,[["__scopeId","data-v-5991e86d"]]);export{oe as default};
