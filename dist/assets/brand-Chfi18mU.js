import{r as _,a as j,c as G,m as Te,b as r,d as a,g as n,w as l,h as d,j as c,t as f,k as u,F,i as O,f as g,s as q,n as B,u as Me,o as i,l as ze,y as Ue}from"./index-B4CbS3Hl.js";import{_ as Ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{M as Se}from"./MoreOutlined-xtgiaAC5.js";const $e={class:"fav-brand-container"},Be={class:"search-filter-section"},Ee={class:"search-card"},Ae={class:"filters-panel"},De={class:"tab-section"},Le={class:"quick-actions-bar"},Re={class:"actions-right"},Ie={class:"main-content"},Ve={key:0,class:"card-view"},Ke={key:0,class:"empty-state"},je={key:1,class:"brand-cards-container"},Ge={class:"card-content"},Oe={class:"brand-header"},qe={class:"brand-logo"},Ye=["src","alt"],Je={key:1,class:"default-logo"},Qe={class:"brand-name-section"},We={class:"brand-name"},Xe={class:"brand-tags"},He={class:"header-actions"},Ze={class:"status-indicators"},et={class:"recent-products"},tt={class:"products-grid"},at={class:"product-image"},st=["src","alt"],lt={key:1,class:"default-product-image"},ot={class:"product-info"},it=["title"],nt={class:"product-price"},rt={class:"brand-details"},dt={class:"detail-row"},ct={class:"time-value"},ut={key:0,class:"detail-row"},ft={class:"favoriter-info"},pt={class:"detail-row"},mt={class:"note-content"},vt={class:"card-actions"},_t={key:2,class:"card-pagination"},yt={key:1,class:"list-view"},gt={key:0,class:"brand-name-cell"},kt={class:"brand-logo-mini"},ht=["src","alt"],bt={key:1,class:"default-logo-mini"},wt={class:"brand-info"},xt=["onClick"],Ct={class:"brand-tags-mini"},Ft={key:0,class:"fas fa-heart bookmark-icon",title:"我的收藏"},Pt=["href"],Tt={key:0,class:"favoriter-cell"},Mt={key:1,class:"self-favorited"},zt={key:3,class:"products-mini"},Ut={class:"product-name-mini"},Nt={class:"product-price-mini"},St={key:0,class:"more-products"},$t={key:5,class:"note-cell"},Bt={key:0,class:"note-text"},Et={key:1,class:"no-note"},At={__name:"brand",setup(Dt){Me();const T=_(!1),M=_("card"),E=_("addTime"),y=j({keyword:"",brandType:""}),z=_(!1),v=_("my"),b=_([]),A=_(null),U=j({note:""}),p=j({current:1,pageSize:12,total:0}),te=G(()=>({current:p.current,pageSize:p.pageSize,total:p.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(t,e)=>`第 ${e[0]}-${e[1]} 条，共 ${t} 条`}));_([]);const D=_(0),P=_(0),L=_(0),N=[{id:"1",brandName:"Apple",logoUrl:"https://via.placeholder.com/80x80/1890ff/ffffff?text=Apple",website:"https://www.apple.com",category:"电子产品",brandType:"recommended",favoriteTime:16409952e5,note:"全球知名科技品牌，产品质量优秀",isFavorited:!0,isMyFavorite:!0,isPublic:!1,favoriters:[{name:"张三",id:"1"},{name:"李四",id:"2"}],recentProducts:[{id:"p1",name:"iPhone 15 Pro",price:"7999",imageUrl:"https://via.placeholder.com/100x100/007ACC/ffffff?text=iPhone"},{id:"p2",name:"MacBook Pro",price:"12999",imageUrl:"https://via.placeholder.com/100x100/007ACC/ffffff?text=MacBook"},{id:"p3",name:"AirPods Pro",price:"1899",imageUrl:"https://via.placeholder.com/100x100/007ACC/ffffff?text=AirPods"}]},{id:"2",brandName:"华为",logoUrl:"https://via.placeholder.com/80x80/52c41a/ffffff?text=华为",website:"https://www.huawei.com",category:"通信设备",brandType:"recommended",favoriteTime:16410816e5,note:"国产优秀品牌，技术实力强",isFavorited:!0,isMyFavorite:!0,isPublic:!0,favoriters:[{name:"张三",id:"1"}],recentProducts:[{id:"p4",name:"Mate 60 Pro",price:"6999",imageUrl:"https://via.placeholder.com/100x100/FF6B35/ffffff?text=Mate60"},{id:"p5",name:"MateBook X",price:"7999",imageUrl:"https://via.placeholder.com/100x100/FF6B35/ffffff?text=MateBook"},{id:"p6",name:"FreeBuds Pro",price:"1299",imageUrl:"https://via.placeholder.com/100x100/FF6B35/ffffff?text=FreeBuds"}]},{id:"3",brandName:"Tesla",logoUrl:"",website:"https://www.tesla.com",category:"新能源汽车",brandType:"normal",favoriteTime:1641168e6,note:"电动汽车领导品牌",isFavorited:!0,isMyFavorite:!1,isPublic:!0,favoriters:[{name:"王五",id:"3"},{name:"赵六",id:"4"},{name:"钱七",id:"5"},{name:"孙八",id:"6"},{name:"李九",id:"7"},{name:"周十",id:"8"},{name:"吴十一",id:"9"}],recentProducts:[{id:"p7",name:"Model Y",price:"263900",imageUrl:"https://via.placeholder.com/100x100/E53E3E/ffffff?text=ModelY"},{id:"p8",name:"Model 3",price:"231900",imageUrl:"https://via.placeholder.com/100x100/E53E3E/ffffff?text=Model3"},{id:"p9",name:"Cybertruck",price:"399000",imageUrl:"https://via.placeholder.com/100x100/E53E3E/ffffff?text=Cybertruck"}]},{id:"4",brandName:"Samsung",logoUrl:"https://via.placeholder.com/80x80/1428a0/ffffff?text=Samsung",website:"https://www.samsung.com",category:"电子产品",brandType:"normal",favoriteTime:16412544e5,note:"韩国知名电子品牌",isFavorited:!0,isMyFavorite:!1,isPublic:!1,favoriters:[{name:"李四",id:"2"},{name:"王五",id:"3"}],recentProducts:[{id:"p10",name:"Galaxy S24",price:"5999",imageUrl:"https://via.placeholder.com/100x100/1428a0/ffffff?text=Galaxy"},{id:"p11",name:"Galaxy Book",price:"6999",imageUrl:"https://via.placeholder.com/100x100/1428a0/ffffff?text=Book"}]}],ae=G(()=>({selectedRowKeys:b.value,onChange:t=>{b.value=t}})),se=()=>{const t=[{title:"品牌名称",dataIndex:"brandName",key:"name",width:250,fixed:"left"},{title:"品牌网站",dataIndex:"website",key:"website",width:200},{title:"品牌类别",dataIndex:"category",key:"category",width:120},{title:"最近商品",key:"recentProducts",width:200},{title:"收藏时间",key:"favoriteTime",width:120,sorter:!0}];return(v.value==="public"||v.value==="all")&&t.push({title:"收藏人",key:"favoriter",width:150}),t.push({title:"备注",key:"note",width:200}),t.push({title:"操作",key:"actions",width:200,fixed:"right"}),t},le=()=>v.value==="my"?ae.value:null,S=G(()=>{let t=[...N];if(v.value==="my"?t=t.filter(m=>m.isMyFavorite):v.value==="public"&&(t=t.filter(m=>m.isPublic)),y.keyword){const m=y.keyword.toLowerCase();t=t.filter(k=>k.brandName.toLowerCase().includes(m))}y.brandType&&(t=t.filter(m=>m.brandType===y.brandType)),E.value==="addTime"?t.sort((m,k)=>k.favoriteTime-m.favoriteTime):E.value==="brandName"&&t.sort((m,k)=>m.brandName.localeCompare(k.brandName)),p.total=t.length;const e=(p.current-1)*p.pageSize,C=e+p.pageSize;return t.slice(e,C)}),oe=()=>v.value==="my"?"暂无我的收藏品牌":v.value==="public"?"暂无公开的收藏品牌":"暂无收藏的品牌",x=async()=>{T.value=!0;try{await new Promise(t=>setTimeout(t,500))}catch{B.error("搜索失败，请重试")}finally{T.value=!1}},ie=()=>{y.keyword="",y.brandType="",p.current=1,x()},ne=t=>{v.value=t,b.value=[],p.current=1,x()},re=(t,e)=>{p.current=t,p.pageSize=e,x()},de=(t,e,C)=>{p.current=t.current,p.pageSize=t.pageSize,C.field&&C.field==="favoriteTime"&&(E.value="addTime"),x()},ce=t=>{const e=b.value.indexOf(t);e>-1?b.value.splice(e,1):b.value.push(t)},ue=t=>{t.target.style.display="none";const e=t.target.nextElementSibling;e&&(e.style.display="flex")},Y=t=>{window.open(t.website,"_blank","noopener,noreferrer")},R=t=>{A.value=t,U.note=t.note||"",z.value=!0},fe=()=>{A.value&&(A.value.note=U.note,B.success("备注保存成功"),z.value=!1)},J=t=>{t.isFavorited=!1,t.isMyFavorite=!1,t.isPublic=!1,B.success(`已将 ${t.brandName} 从收藏夹移除`),D.value--,t.isPublic&&P.value--,L.value--},Q=t=>{t.isPublic=!t.isPublic,B.success(`已将 ${t.brandName} 设为${t.isPublic?"公开":"私密"}`),t.isPublic?P.value++:P.value--},W=t=>!t||t.length===0?"":t.length===1?t[0].name:t.length<=3?t.map(e=>e.name).join("、"):`${t[0].name}等${t.length}人`,X=t=>t?new Date(t).toLocaleDateString("zh-CN"):"";return Te(()=>{D.value=N.filter(t=>t.isMyFavorite).length,P.value=N.filter(t=>t.isPublic).length,L.value=N.filter(t=>t.isFavorited).length}),(t,e)=>{const C=d("a-input"),m=d("a-form-item"),k=d("a-col"),H=d("a-select-option"),pe=d("a-select"),h=d("a-button"),I=d("a-space"),me=d("a-row"),V=d("a-tab-pane"),ve=d("a-tabs"),Z=d("a-radio-button"),_e=d("a-radio-group"),ye=d("a-empty"),ge=d("a-checkbox"),$=d("a-tag"),K=d("a-menu-item"),ke=d("a-menu"),he=d("a-dropdown"),ee=d("a-tooltip"),be=d("a-card"),we=d("a-pagination"),xe=d("a-table"),Ce=d("a-textarea"),Fe=d("a-form"),Pe=d("a-modal");return i(),r("div",$e,[a("div",Be,[a("div",Ee,[a("div",Ae,[n(me,{gutter:16},{default:l(()=>[n(k,{span:8},{default:l(()=>[n(m,{label:"品牌名称"},{default:l(()=>[n(C,{value:y.keyword,"onUpdate:value":e[0]||(e[0]=s=>y.keyword=s),placeholder:"搜索收藏的品牌","allow-clear":"",onPressEnter:x},null,8,["value"])]),_:1})]),_:1}),n(k,{span:6},{default:l(()=>[n(m,{label:"品牌类型"},{default:l(()=>[n(pe,{value:y.brandType,"onUpdate:value":e[1]||(e[1]=s=>y.brandType=s),placeholder:"筛选类型","allow-clear":""},{default:l(()=>[n(H,{value:""},{default:l(()=>e[9]||(e[9]=[c("全部")])),_:1}),n(H,{value:"recommended"},{default:l(()=>e[10]||(e[10]=[c("推荐品牌")])),_:1})]),_:1},8,["value"])]),_:1})]),_:1}),n(k,{span:6},{default:l(()=>[n(m,{style:{"margin-bottom":"0"}},{default:l(()=>[n(I,null,{default:l(()=>[n(h,{type:"primary",onClick:x,loading:T.value},{default:l(()=>e[11]||(e[11]=[a("i",{class:"fas fa-search"},null,-1),c(" 搜索 ")])),_:1},8,["loading"]),n(h,{onClick:ie},{default:l(()=>e[12]||(e[12]=[a("i",{class:"fas fa-undo"},null,-1),c(" 重置 ")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])])]),a("div",De,[n(ve,{activeKey:v.value,"onUpdate:activeKey":e[2]||(e[2]=s=>v.value=s),onChange:ne,class:"favorites-tabs"},{default:l(()=>[n(V,{key:"my",tab:"我的收藏"},{tab:l(()=>[a("span",null,[e[13]||(e[13]=a("i",{class:"fas fa-heart"},null,-1)),c(" 我的收藏 ("+f(D.value)+") ",1)])]),_:1}),n(V,{key:"public",tab:"公开的收藏"},{tab:l(()=>[a("span",null,[e[14]||(e[14]=a("i",{class:"fas fa-share-alt"},null,-1)),c(" 公开的收藏 ("+f(P.value)+") ",1)])]),_:1}),n(V,{key:"all",tab:"全部"},{tab:l(()=>[a("span",null,[e[15]||(e[15]=a("i",{class:"fas fa-list"},null,-1)),c(" 全部 ("+f(L.value)+") ",1)])]),_:1})]),_:1},8,["activeKey"])]),a("div",Le,[e[18]||(e[18]=a("div",{class:"actions-left"},null,-1)),a("div",Re,[n(I,null,{default:l(()=>[n(_e,{value:M.value,"onUpdate:value":e[3]||(e[3]=s=>M.value=s),"button-style":"solid",size:"small"},{default:l(()=>[n(Z,{value:"card"},{default:l(()=>e[16]||(e[16]=[a("i",{class:"fas fa-th-large"},null,-1),c(" 卡片 ")])),_:1}),n(Z,{value:"list"},{default:l(()=>e[17]||(e[17]=[a("i",{class:"fas fa-list"},null,-1),c(" 列表 ")])),_:1})]),_:1},8,["value"])]),_:1})])]),a("div",Ie,[M.value==="card"?(i(),r("div",Ve,[S.value.length===0?(i(),r("div",Ke,[n(ye,{description:oe()},{image:l(()=>e[19]||(e[19]=[a("i",{class:"fas fa-star-half-alt",style:{"font-size":"48px",color:"#d9d9d9"}},null,-1)])),default:l(()=>[n(h,{type:"primary",onClick:e[4]||(e[4]=s=>t.$router.push("/workspace/brand/index"))},{default:l(()=>e[20]||(e[20]=[a("i",{class:"fas fa-search"},null,-1),c(" 去发现品牌 ")])),_:1})]),_:1},8,["description"])])):(i(),r("div",je,[(i(!0),r(F,null,O(S.value,s=>(i(),r("div",{key:s.id,class:"brand-card-wrapper"},[n(be,{class:q(["brand-card",{selected:b.value.includes(s.id)}])},{default:l(()=>[a("div",Ge,[a("div",Oe,[v.value==="my"||v.value==="all"&&s.isMyFavorite?(i(),g(ge,{key:0,checked:b.value.includes(s.id),onChange:()=>ce(s.id),class:"card-checkbox"},null,8,["checked","onChange"])):u("",!0),a("div",qe,[s.logoUrl?(i(),r("img",{key:0,src:s.logoUrl,alt:s.brandName,onError:ue},null,40,Ye)):(i(),r("div",Je,e[21]||(e[21]=[a("i",{class:"fas fa-star"},null,-1)])))]),a("div",Qe,[a("div",We,f(s.brandName),1),a("div",Xe,[s.brandType==="recommended"?(i(),g($,{key:0,color:"gold",class:"recommended-tag",size:"small"},{default:l(()=>e[22]||(e[22]=[a("i",{class:"fas fa-crown"},null,-1),c(" 推荐品牌 ")])),_:1})):u("",!0),s.isPublic&&s.isMyFavorite?(i(),g($,{key:1,color:"green",size:"small"},{default:l(()=>e[23]||(e[23]=[a("i",{class:"fas fa-share-alt"},null,-1),c(" 已公开 ")])),_:1})):u("",!0)])]),a("div",He,[s.isMyFavorite?(i(),g(he,{key:0,trigger:["click"],placement:"bottomRight"},{overlay:l(()=>[n(ke,null,{default:l(()=>[n(K,{key:"edit-note",onClick:o=>R(s)},{default:l(()=>e[24]||(e[24]=[a("i",{class:"fas fa-edit"},null,-1),c(" 编辑备注 ")])),_:2},1032,["onClick"]),n(K,{key:"toggle-public",onClick:o=>Q(s)},{default:l(()=>[a("i",{class:q(s.isPublic?"fas fa-eye-slash":"fas fa-share-alt")},null,2),c(" "+f(s.isPublic?"取消公开":"设为公开"),1)]),_:2},1032,["onClick"]),n(K,{key:"remove",onClick:o=>J(s),class:"danger-item"},{default:l(()=>e[25]||(e[25]=[a("i",{class:"fas fa-heart-broken"},null,-1),c(" 取消收藏 ")])),_:2},1032,["onClick"])]),_:2},1024)]),default:l(()=>[n(h,{type:"text",size:"small",class:"action-menu-btn"},{default:l(()=>[n(ze(Se))]),_:1})]),_:2},1024)):u("",!0),a("div",Ze,[s.isPublic&&s.isMyFavorite?(i(),g(ee,{key:0,title:"已公开"},{default:l(()=>e[26]||(e[26]=[a("i",{class:"fas fa-share-alt status-icon public"},null,-1)])),_:1})):u("",!0),s.isMyFavorite?(i(),g(ee,{key:1,title:"我的收藏"},{default:l(()=>e[27]||(e[27]=[a("i",{class:"fas fa-heart status-icon favorite"},null,-1)])),_:1})):u("",!0)])])]),a("div",et,[e[29]||(e[29]=a("div",{class:"products-title"},"最近上架商品",-1)),a("div",tt,[(i(!0),r(F,null,O(s.recentProducts,o=>(i(),r("div",{key:o.id,class:"product-item"},[a("div",at,[o.imageUrl?(i(),r("img",{key:0,src:o.imageUrl,alt:o.name},null,8,st)):(i(),r("div",lt,e[28]||(e[28]=[a("i",{class:"fas fa-cube"},null,-1)])))]),a("div",ot,[a("div",{class:"product-name",title:o.name},f(o.name),9,it),a("div",nt,"¥"+f(o.price),1)])]))),128))])]),a("div",rt,[a("div",dt,[e[30]||(e[30]=a("span",{class:"detail-label"},"收藏时间",-1)),a("div",ct,f(X(s.favoriteTime)),1)]),s.isMyFavorite?u("",!0):(i(),r("div",ut,[e[31]||(e[31]=a("span",{class:"detail-label"},"收藏人",-1)),a("div",ft,f(W(s.favoriters)),1)])),a("div",pt,[e[32]||(e[32]=a("span",{class:"detail-label"},"备注",-1)),a("div",mt,f(s.note||"暂无备注"),1)])]),a("div",vt,[n(h,{type:"primary",onClick:Ue(o=>Y(s),["stop"]),block:""},{default:l(()=>e[33]||(e[33]=[a("i",{class:"fas fa-external-link-alt"},null,-1),c(" 进入主页 ")])),_:2},1032,["onClick"])])])]),_:2},1032,["class"])]))),128))])),S.value.length>0?(i(),r("div",_t,[n(we,{current:p.current,"onUpdate:current":e[5]||(e[5]=s=>p.current=s),"page-size":p.pageSize,"onUpdate:pageSize":e[6]||(e[6]=s=>p.pageSize=s),total:p.total,"show-size-changer":!0,"show-quick-jumper":!0,"show-total":(s,o)=>`第 ${o[0]}-${o[1]} 条，共 ${s} 条`,onChange:re},null,8,["current","page-size","total","show-total"])])):u("",!0)])):M.value==="list"?(i(),r("div",yt,[n(xe,{columns:se(),"data-source":S.value,pagination:te.value,loading:T.value,"row-selection":le(),"row-key":"id",onChange:de,class:"brand-table",size:"small",bordered:""},{bodyCell:l(({column:s,record:o})=>[s.key==="name"?(i(),r("div",gt,[a("div",kt,[o.logoUrl?(i(),r("img",{key:0,src:o.logoUrl,alt:o.brandName},null,8,ht)):(i(),r("div",bt,e[34]||(e[34]=[a("i",{class:"fas fa-star"},null,-1)])))]),a("div",wt,[a("a",{onClick:w=>Y(o),style:{color:"#f94c30","font-weight":"500"}},f(o.brandName),9,xt),a("div",Ct,[o.brandType==="recommended"?(i(),g($,{key:0,color:"gold",size:"small"},{default:l(()=>e[35]||(e[35]=[a("i",{class:"fas fa-crown"},null,-1),c(" 推荐品牌 ")])),_:1})):u("",!0),o.isPublic&&o.isMyFavorite?(i(),g($,{key:1,color:"green",size:"small"},{default:l(()=>e[36]||(e[36]=[a("i",{class:"fas fa-share-alt"},null,-1),c(" 已公开 ")])),_:1})):u("",!0)])]),o.isMyFavorite?(i(),r("i",Ft)):u("",!0)])):u("",!0),s.key==="website"?(i(),r("a",{key:1,href:o.website,target:"_blank",rel:"noopener noreferrer"},f(o.website),9,Pt)):u("",!0),s.key==="favoriter"?(i(),r(F,{key:2},[o.isMyFavorite?(i(),r("span",Mt,"我的收藏")):(i(),r("div",Tt,f(W(o.favoriters)),1))],64)):u("",!0),s.key==="recentProducts"?(i(),r("div",zt,[(i(!0),r(F,null,O(o.recentProducts.slice(0,2),w=>(i(),r("div",{key:w.id,class:"product-mini"},[a("span",Ut,f(w.name),1),a("span",Nt,"¥"+f(w.price),1)]))),128)),o.recentProducts.length>2?(i(),r("span",St," +"+f(o.recentProducts.length-2)+"个 ",1)):u("",!0)])):u("",!0),s.key==="favoriteTime"?(i(),r(F,{key:4},[c(f(X(o.favoriteTime)),1)],64)):u("",!0),s.key==="note"?(i(),r("div",$t,[o.note?(i(),r("span",Bt,f(o.note),1)):(i(),r("span",Et,"暂无备注")),o.isMyFavorite?(i(),g(h,{key:2,type:"link",size:"small",onClick:w=>R(o)},{default:l(()=>e[37]||(e[37]=[a("i",{class:"fas fa-edit"},null,-1)])),_:2},1032,["onClick"])):u("",!0)])):u("",!0),s.key==="actions"?(i(),g(I,{key:6},{default:l(()=>[o.isMyFavorite?(i(),r(F,{key:0},[n(h,{type:"link",size:"small",onClick:w=>R(o)},{default:l(()=>e[38]||(e[38]=[a("i",{class:"fas fa-edit"},null,-1),c(" 编辑备注 ")])),_:2},1032,["onClick"]),n(h,{type:"link",size:"small",onClick:w=>Q(o)},{default:l(()=>[a("i",{class:q(o.isPublic?"fas fa-eye-slash":"fas fa-share-alt")},null,2),c(" "+f(o.isPublic?"取消公开":"设为公开"),1)]),_:2},1032,["onClick"]),n(h,{type:"link",size:"small",danger:"",onClick:w=>J(o)},{default:l(()=>e[39]||(e[39]=[a("i",{class:"fas fa-heart-broken"},null,-1),c(" 取消收藏 ")])),_:2},1032,["onClick"])],64)):u("",!0)]),_:2},1024)):u("",!0)]),_:1},8,["columns","data-source","pagination","loading","row-selection"])])):u("",!0)]),n(Pe,{open:z.value,"onUpdate:open":e[8]||(e[8]=s=>z.value=s),title:"编辑备注",width:"500px",onOk:fe},{default:l(()=>[n(Fe,{layout:"vertical"},{default:l(()=>[n(m,{label:"备注信息"},{default:l(()=>[n(Ce,{value:U.note,"onUpdate:value":e[7]||(e[7]=s=>U.note=s),placeholder:"请输入备注信息...",rows:4,"show-count":"",maxlength:200},null,8,["value"])]),_:1})]),_:1})]),_:1},8,["open"])])}}},Vt=Ne(At,[["__scopeId","data-v-4b0bf3dd"]]);export{Vt as default};
