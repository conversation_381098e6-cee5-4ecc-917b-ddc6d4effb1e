import{r as P,p as xe,c as B,m as he,b as y,g as p,d as a,w as l,h as q,n as k,u as qe,o as d,f as m,k as r,j as n,t as u,l as z,D as Se,G as we,F as _}from"./index-B4CbS3Hl.js";import{S as Ie}from"./supplierModal-DiF_wNpr.js";import{_ as Ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Pe={class:"rfq-detail-container"},be={class:"info-item"},Me={class:"value"},Te={class:"info-item"},$e={class:"value"},Be={class:"info-item"},De={class:"value"},Ae={class:"info-item"},Oe={class:"value"},je={class:"info-item"},Ne={class:"value"},Qe={class:"info-item"},Fe={class:"value"},Ee={class:"info-item"},Ve={class:"value"},Ke={class:"info-item"},Le={class:"value"},ze={class:"info-item"},Ge={class:"value"},Ue={class:"info-item"},He={class:"value important"},Je={class:"table-operations"},We={class:"selection-summary"},Xe={style:{color:"#666"}},Ye={class:"summary-content"},Ze={key:10,class:"quote-progress"},Re={class:"quoted-count"},et={class:"rejected-count"},tt=["onClick"],at=["onClick"],nt=["onClick"],lt=["onClick"],st=["onClick"],it=["onClick"],dt={style:{margin:"12px"}},ot={class:"bottom-actions"},rt={__name:"rfqDetail",setup(ut){const G=xe(),U=qe(),K=P(G.params.rfqNo),c=P({id:"",rfqNo:"RFQ-2023-0001",status:"inProgress",createTime:"2023-10-15 09:30:00",rfqTime:"2023-10-15 10:00:00",deadline:"2023-10-25 18:00:00",endTime:"2023-10-23 16:30:00",creator:"张三",contactPhone:"13800138000",materialModelCount:4,materials:[]}),D=P([]),w=P([]),b=P(!1),H=P(!1),A=P(null),J=[{title:"物料名称",dataIndex:"name",key:"name",width:180,fixed:"left"},{title:"型号",dataIndex:"model",key:"model",width:180,fixed:"left"},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"接受平替",dataIndex:"acceptAlternative",key:"acceptAlternative",width:100},{title:"平替品牌",dataIndex:"alternativeBrand",key:"alternativeBrand",width:120},{title:"平替型号",dataIndex:"alternativeModel",key:"alternativeModel",width:120},{title:"单价 (¥)",dataIndex:"unitPrice",key:"unitPrice",width:100},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:120},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:100},{title:"交期",dataIndex:"delivery",key:"delivery",width:100},{title:"期望交期",dataIndex:"expectedDelivery",key:"expectedDelivery",width:100},{title:"询价时间",dataIndex:"rfqTime",key:"rfqTime",width:150},{title:"结束时间",dataIndex:"endTime",key:"endTime",width:150},{title:"截止时间",dataIndex:"deadline",key:"deadline",width:150},{title:"询价状态",dataIndex:"status",key:"status",width:100},{title:"报价进度",dataIndex:"quoteProgress",key:"quoteProgress",width:150,customRender:({record:t})=>te(t)},{title:"操作",dataIndex:"action",key:"action",fixed:"right",width:275}],W=t=>{const e=[];return e.push({title:"供应商",dataIndex:"supplierName",key:"supplierName",width:150,customRender:({text:o,record:v})=>o||v.name||"-"}),t&&e.push({title:"平替品牌",dataIndex:"alternativeBrand",key:"alternativeBrand",width:120},{title:"平替型号",dataIndex:"alternativeModel",key:"alternativeModel",width:120}),e.push({title:"报价 (¥)",dataIndex:"price",key:"price",width:100},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:100},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:150},{title:"承诺交期",dataIndex:"promisedDelivery",key:"promisedDelivery",width:120},{title:"有效期",dataIndex:"validityPeriod",key:"validityPeriod",width:120},{title:"报价时间",dataIndex:"quoteTime",key:"quoteTime",width:150},{title:"报价状态",dataIndex:"status",key:"status",width:100},{title:"报价类型",dataIndex:"quoteType",key:"quoteType",width:100,customRender:({text:o})=>o==="platform"?"平台报价":"外部报价"},{title:"操作",dataIndex:"action",key:"action",width:100}),e},E=B(()=>c.value.status==="notStarted"),O=B(()=>c.value.status==="notStarted"),j=B(()=>c.value.status==="accepted"),V=B(()=>["accepted","expired","invalid","cancelled"].includes(c.value.status)),N=B(()=>c.value.status==="inProgress"),X=()=>{U.go(-1)},I=t=>t||"",Y=t=>({notStarted:"未开始",inProgress:"询价中",accepted:"已采纳",expired:"已过期",invalid:"已失效",cancelled:"已取消"})[t]||t,Z=t=>({notStarted:"default",inProgress:"blue",accepted:"green",expired:"orange",invalid:"red",cancelled:"red"})[t]||"default",R=t=>({pending:"blue",quoted:"green",rejected:"red",expired:"orange"})[t]||"default",ee=t=>({pending:"待报价",quoted:"已报价",rejected:"已拒绝",expired:"已过期"})[t]||"未知",te=t=>{if(!t.suppliers||t.suppliers.length===0)return"供应商数：0，已报价：0，已拒绝：0";const e=t.suppliers.length,o=t.suppliers.filter(S=>S.status==="quoted").length,v=t.suppliers.filter(S=>S.status==="rejected").length;return`供应商数：${e}，已报价：${o}，已拒绝：${v}`},ae=t=>t.suppliers?t.suppliers.length:0,ne=t=>t.suppliers?t.suppliers.filter(e=>e.status==="quoted").length:0,le=t=>t.suppliers?t.suppliers.filter(e=>e.status==="rejected").length:0,se=()=>c.value.materials.reduce((t,e)=>t+e.quantity,0),L=()=>`¥${c.value.materials.filter(o=>w.value.includes(o.id)).reduce((o,v)=>{const S=v.selectedSupplier?v.selectedSupplier.totalPrice:0;return o+S},0).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})}`,ie=t=>{w.value=t},de=(t,e)=>{if(t)D.value.push(e.id);else{const o=D.value.indexOf(e.id);o>-1&&D.value.splice(o,1)}},oe=(t,e)=>{if(e){e.suppliers.forEach(v=>{v.isSelected=!1});const o=e.suppliers.find(v=>v.id===t.id);o&&(o.isSelected=!0,e.selectedSupplier=o,e.acceptAlternative&&o.alternativeBrand&&o.alternativeModel&&(e.alternativeBrand=o.alternativeBrand,e.alternativeModel=o.alternativeModel),k.success(`已选择供应商：${o.name}`))}},re=t=>{A.value=t,b.value=!0},ue=t=>{k.info(`启动询价：${t.name}`)},pe=t=>{k.info(`转采购单：${t.name}`)},ce=t=>{k.info(`再次询价：${t.name}`)},ve=t=>{k.info(`取消询价：${t.name}`)},ye=t=>{k.info(`查看询价历史：${t.name}`)},fe=({key:t})=>{if(w.value.length===0){k.warning("请先选择要操作的物料");return}const e=c.value.materials.filter(o=>w.value.includes(o.id));switch(t){case"configSupplier":k.info(`批量配置供应商：${e.length}个物料`);break;case"startInquiry":k.info(`批量启动询价：${e.length}个物料`);break;case"toPurchaseOrder":k.info(`批量转采购单：${e.length}个物料`);break;case"cancel":k.info(`批量取消：${e.length}个物料`);break}},me=()=>{if(w.value.length===0){k.warning("请先选择要分配的物料");return}k.info("智能分配供应商")},_e=t=>{A.value&&t&&t.length>0&&(t.forEach(e=>{if(A.value.suppliers.findIndex(v=>v.id===e.id)===-1){const v={id:e.id,name:e.name,supplierName:e.name,price:null,totalPrice:null,minOrderQuantity:1,promisedDelivery:"",validityPeriod:"",quoteTime:"",status:"pending",isSelected:!1,quoteType:"platform"};A.value.suppliers.push(v)}}),b.value=!1,k.success(`已成功添加${t.length}个供应商`))},ke=()=>{b.value=!1},ge=async()=>{try{console.log("获取询价单详情，询价单号：",K.value),await new Promise(t=>setTimeout(t,500)),c.value.rfqNo=K.value,c.value.materials=Array.from({length:4}).map((t,e)=>{const o=Math.floor(Math.random()*100+10),v=e%4===0?"notStarted":e%4===1?"inProgress":e%4===2?"accepted":"expired",S=Array.from({length:3}).map((h,g)=>{const T=g%4===0?"pending":g%4===1?"quoted":g%4===2?"rejected":"expired",F=Math.floor(Math.random()*1e3+100),C=g===1,$=["兼容品牌A","兼容品牌B","通用品牌C"],f=["ALT-001","COMP-002","GEN-003"],s=g%2===0?"platform":"external",i={platform:["严选供应商","深圳市电子科技有限公司","北京智能制造有限公司"],external:["上海精密器件有限公司","广州电子元件供应商","天津工业设备公司"]};return{id:`supplier-${e}-${g}`,name:`供应商 ${g+1}`,supplierName:i[s][g%i[s].length],price:F,totalPrice:F*o,minOrderQuantity:Math.floor(Math.random()*100+10),promisedDelivery:`${Math.floor(Math.random()*30+15)}天`,validityPeriod:"2023-11-05",quoteTime:"2023-10-16 10:30:00",status:T,isSelected:!1,quoteType:s,alternativeBrand:C&&T==="quoted"?$[g%$.length]:null,alternativeModel:C&&T==="quoted"?f[g%f.length]:null}});let x=null,Q=null,M=null;if(v==="accepted"){const h=S.find(g=>g.status==="quoted");h&&(h.isSelected=!0,x=h,e%3===0&&h.alternativeBrand&&h.alternativeModel&&(Q=h.alternativeBrand,M=h.alternativeModel))}return{id:`material-${e}`,name:`测试物料 ${e+1}`,model:`MODEL-${100+e}`,brand:e%3===0?"A":e%3===1?"B":"C",quantity:o,expectedDelivery:"30天",rfqTime:v==="notStarted"?"":"2023-10-15 10:00:00",endTime:"2023-10-23 16:30:00",deadline:"2023-10-25 18:00:00",status:v,suppliers:S,selectedSupplier:x,acceptAlternative:e%3===0,alternativeBrand:Q,alternativeModel:M}})}catch{k.error("获取询价单详情失败")}};return he(()=>{ge()}),(t,e)=>{const o=q("a-button"),v=q("a-space"),S=q("a-page-header"),x=q("a-col"),Q=q("a-row"),M=q("a-card"),h=q("a-menu-item"),g=q("a-menu"),T=q("a-dropdown"),F=q("a-tooltip"),C=q("a-tag"),$=q("a-table");return d(),y("div",Pe,[p(S,{title:"询价单详情 - "+c.value.rfqNo,onBack:X},{extra:l(()=>[p(v,null,{default:l(()=>[O.value?(d(),m(o,{key:0,type:"primary"},{default:l(()=>e[1]||(e[1]=[n("启动询价")])),_:1})):r("",!0),j.value?(d(),m(o,{key:1,type:"primary"},{default:l(()=>e[2]||(e[2]=[n("转采购单")])),_:1})):r("",!0),V.value?(d(),m(o,{key:2},{default:l(()=>e[3]||(e[3]=[n("再次询价")])),_:1})):r("",!0),N.value?(d(),m(o,{key:3,danger:""},{default:l(()=>e[4]||(e[4]=[n("取消询价")])),_:1})):r("",!0)]),_:1})]),_:1},8,["title"]),p(M,{title:"基本信息",class:"detail-card"},{default:l(()=>[p(Q,{gutter:24},{default:l(()=>[p(x,{span:8},{default:l(()=>[a("div",be,[e[5]||(e[5]=a("span",{class:"label"},"询价单号：",-1)),a("span",Me,u(c.value.rfqNo),1)])]),_:1}),p(x,{span:8},{default:l(()=>[a("div",Te,[e[6]||(e[6]=a("span",{class:"label"},"创建时间：",-1)),a("span",$e,u(I(c.value.createTime)),1)])]),_:1}),p(x,{span:8},{default:l(()=>[a("div",Be,[e[7]||(e[7]=a("span",{class:"label"},"询价时间：",-1)),a("span",De,u(c.value.status==="notStarted"?"-":I(c.value.rfqTime)),1)])]),_:1}),p(x,{span:8},{default:l(()=>[a("div",Ae,[e[8]||(e[8]=a("span",{class:"label"},"截止时间：",-1)),a("span",Oe,u(I(c.value.deadline)),1)])]),_:1}),p(x,{span:8},{default:l(()=>[a("div",je,[e[9]||(e[9]=a("span",{class:"label"},"结束时间：",-1)),a("span",Ne,u(c.value.status==="inProgress"?"-":I(c.value.endTime)),1)])]),_:1}),p(x,{span:8},{default:l(()=>[a("div",Qe,[e[10]||(e[10]=a("span",{class:"label"},"创建人：",-1)),a("span",Fe,u(c.value.creator),1)])]),_:1}),p(x,{span:8},{default:l(()=>[a("div",Ee,[e[11]||(e[11]=a("span",{class:"label"},"联系电话：",-1)),a("span",Ve,u(c.value.contactPhone),1)])]),_:1}),p(x,{span:8},{default:l(()=>[a("div",Ke,[e[12]||(e[12]=a("span",{class:"label"},"物料型号数：",-1)),a("span",Le,u(c.value.materialModelCount)+" 种",1)])]),_:1}),p(x,{span:8},{default:l(()=>[a("div",ze,[e[13]||(e[13]=a("span",{class:"label"},"物料总数：",-1)),a("span",Ge,u(se())+" 件",1)])]),_:1}),p(x,{span:8},{default:l(()=>[a("div",Ue,[e[14]||(e[14]=a("span",{class:"label"},"物料总价：",-1)),a("span",He,u(L()),1)])]),_:1})]),_:1})]),_:1}),p(M,{title:"物料信息",class:"detail-card"},{default:l(()=>[a("div",Je,[p(v,null,{default:l(()=>[p(T,null,{overlay:l(()=>[p(g,{onClick:fe},{default:l(()=>[E.value?(d(),m(h,{key:"configSupplier"},{default:l(()=>e[16]||(e[16]=[n("配置供应商")])),_:1})):r("",!0),O.value?(d(),m(h,{key:"startInquiry"},{default:l(()=>e[17]||(e[17]=[n("启动询价")])),_:1})):r("",!0),j.value?(d(),m(h,{key:"toPurchaseOrder"},{default:l(()=>e[18]||(e[18]=[n("转采购单")])),_:1})):r("",!0),N.value?(d(),m(h,{key:"cancel"},{default:l(()=>e[19]||(e[19]=[n("取消")])),_:1})):r("",!0)]),_:1})]),default:l(()=>[p(o,{type:"primary"},{default:l(()=>[e[15]||(e[15]=n(" 批量操作 ")),p(z(Se))]),_:1})]),_:1}),E.value?(d(),m(o,{key:0,type:"primary",onClick:me},{default:l(()=>e[20]||(e[20]=[n("智能分配")])),_:1})):r("",!0)]),_:1})]),a("div",We,[a("div",null,[p(F,{placement:"top"},{title:l(()=>e[21]||(e[21]=[a("div",null,"1. 本表中的价格若未做特殊说明，均为含税价格。",-1),a("div",null,[n("2. 在询价单转为订单后，将根据订单总价加收运费，具体规则如下："),a("br"),n("订单总金额¥0.00 - ¥499.99，运费¥15.00"),a("br"),n("订单总金额¥500.00 - ¥999.99，运费¥8.00"),a("br"),n("订单总金额¥1000以上，免运费")],-1)])),default:l(()=>[a("span",Xe,[p(z(we),{style:{"margin-right":"4px"}}),e[22]||(e[22]=n("价格与运费说明"))])]),_:1})]),a("div",Ye,[a("span",null,[e[23]||(e[23]=n("已选择：")),p(C,{color:"blue"},{default:l(()=>[n(u(w.value.length),1)]),_:1}),e[24]||(e[24]=n(" 个物料"))]),a("span",null,[e[25]||(e[25]=n("总金额：")),p(C,{color:"red"},{default:l(()=>[n(u(L()),1)]),_:1})])])]),p($,{columns:J,"data-source":c.value.materials,size:"middle",pagination:{pageSize:20},"row-key":"id","row-selection":{selectedRowKeys:w.value,onChange:ie},bordered:"",scroll:{x:1500},expandedRowKeys:D.value,onExpand:de},{bodyCell:l(({column:f,record:s})=>[f.dataIndex==="acceptAlternative"?(d(),y(_,{key:0},[n(u(s.acceptAlternative?"是":"否"),1)],64)):r("",!0),f.dataIndex==="alternativeBrand"?(d(),y(_,{key:1},[n(u(s.acceptAlternative&&s.alternativeBrand?s.alternativeBrand:"-"),1)],64)):r("",!0),f.dataIndex==="alternativeModel"?(d(),y(_,{key:2},[n(u(s.acceptAlternative&&s.alternativeModel?s.alternativeModel:"-"),1)],64)):r("",!0),f.dataIndex==="unitPrice"?(d(),y(_,{key:3},[n(u(s.selectedSupplier?`¥${s.selectedSupplier.price.toFixed(2)}`:"-"),1)],64)):r("",!0),f.dataIndex==="totalPrice"?(d(),y(_,{key:4},[n(u(s.selectedSupplier?`¥${s.selectedSupplier.totalPrice.toFixed(2)}`:"-"),1)],64)):r("",!0),f.dataIndex==="minOrderQuantity"?(d(),y(_,{key:5},[n(u(s.selectedSupplier?s.selectedSupplier.minOrderQuantity:"-"),1)],64)):r("",!0),f.dataIndex==="delivery"?(d(),y(_,{key:6},[n(u(s.selectedSupplier?s.selectedSupplier.promisedDelivery:"-"),1)],64)):r("",!0),f.dataIndex==="rfqTime"?(d(),y(_,{key:7},[n(u(c.value.status==="notStarted"?"-":I(s.rfqTime)),1)],64)):r("",!0),f.dataIndex==="endTime"?(d(),y(_,{key:8},[n(u(c.value.status==="inProgress"?"-":I(s.endTime)),1)],64)):r("",!0),f.dataIndex==="status"?(d(),m(C,{key:9,color:Z(s.status)},{default:l(()=>[n(u(Y(s.status)),1)]),_:2},1032,["color"])):r("",!0),f.dataIndex==="quoteProgress"?(d(),y("div",Ze,[a("span",null,"供应商数："+u(ae(s))+"，",1),a("span",null,[e[26]||(e[26]=n("已报价：")),a("span",Re,u(ne(s)),1),e[27]||(e[27]=n("，"))]),a("span",null,[e[28]||(e[28]=n("已拒绝：")),a("span",et,u(le(s)),1)])])):r("",!0),f.dataIndex==="action"?(d(),m(v,{key:11},{default:l(()=>[E.value?(d(),y("a",{key:0,onClick:i=>re(s)},"配置供应商",8,tt)):r("",!0),O.value?(d(),y("a",{key:1,onClick:i=>ue(s)},"启动询价",8,at)):r("",!0),j.value?(d(),y("a",{key:2,onClick:i=>pe(s)},"转采购单",8,nt)):r("",!0),V.value?(d(),y("a",{key:3,onClick:i=>ce(s)},"再次询价",8,lt)):r("",!0),N.value?(d(),y("a",{key:4,onClick:i=>ve(s),class:"danger-link"},"取消",8,st)):r("",!0),a("a",{onClick:i=>ye(s)},"询价历史",8,it)]),_:2},1024)):r("",!0)]),expandedRowRender:l(({record:f})=>[a("div",dt,[p($,{size:"small",columns:W(f.acceptAlternative),"data-source":f.suppliers,pagination:!1,"row-key":"id",bordered:""},{bodyCell:l(({column:s,record:i})=>[s.dataIndex==="alternativeBrand"?(d(),y(_,{key:0},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.alternativeBrand||"-"),1)],64)):r("",!0),s.dataIndex==="alternativeModel"?(d(),y(_,{key:1},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.alternativeModel||"-"),1)],64)):r("",!0),s.dataIndex==="price"?(d(),y(_,{key:2},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.price?`¥${i.price.toFixed(2)}`:""),1)],64)):r("",!0),s.dataIndex==="totalPrice"?(d(),y(_,{key:3},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.totalPrice?`¥${i.totalPrice.toFixed(2)}`:""),1)],64)):r("",!0),s.dataIndex==="promisedDelivery"?(d(),y(_,{key:4},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.promisedDelivery),1)],64)):r("",!0),s.dataIndex==="validityPeriod"?(d(),y(_,{key:5},[n(u(i.status==="pending"||i.status==="rejected"?"-":i.validityPeriod),1)],64)):r("",!0),s.dataIndex==="quoteTime"?(d(),y(_,{key:6},[n(u(i.status==="pending"||i.status==="rejected"?"-":I(i.quoteTime)),1)],64)):r("",!0),s.dataIndex==="status"?(d(),m(C,{key:7,color:R(i.status)},{default:l(()=>[n(u(ee(i.status)),1)]),_:2},1032,["color"])):r("",!0),s.dataIndex==="action"?(d(),m(o,{key:8,type:"link",disabled:i.isSelected||i.status==="pending"||i.status==="rejected"||i.quoteType==="external",onClick:pt=>oe(i,f)},{default:l(()=>[n(u(i.isSelected?"已选择":"选择"),1)]),_:2},1032,["type","disabled","onClick"])):r("",!0)]),_:2},1032,["columns","data-source"])])]),_:1},8,["data-source","row-selection","expandedRowKeys"])]),_:1}),a("div",ot,[p(v,null,{default:l(()=>[O.value?(d(),m(o,{key:0,type:"primary"},{default:l(()=>e[29]||(e[29]=[n("启动询价")])),_:1})):r("",!0),j.value?(d(),m(o,{key:1,type:"primary"},{default:l(()=>e[30]||(e[30]=[n("转采购单")])),_:1})):r("",!0),V.value?(d(),m(o,{key:2},{default:l(()=>e[31]||(e[31]=[n("再次询价")])),_:1})):r("",!0),N.value?(d(),m(o,{key:3,danger:""},{default:l(()=>e[32]||(e[32]=[n("取消询价")])),_:1})):r("",!0)]),_:1})]),p(Ie,{title:"配置供应商",visible:b.value,"confirm-loading":H.value,"onUpdate:visible":e[0]||(e[0]=f=>b.value=f),onOk:_e,onCancel:ke},null,8,["visible","confirm-loading"])])}}},ft=Ce(rt,[["__scopeId","data-v-76981b19"]]);export{ft as default};
