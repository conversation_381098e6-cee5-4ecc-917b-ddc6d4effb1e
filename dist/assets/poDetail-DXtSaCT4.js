import{r as k,p as Le,c as N,m as Fe,b as _,g as n,d as o,w as a,h as c,n as h,u as He,o as u,f as I,k as f,j as y,F as C,i as F,t as s,z as d,l as Ve,E as H}from"./index-B4CbS3Hl.js";import{_ as Ue}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{I as Xe}from"./InboxOutlined-bBXR2cOR.js";const je={class:"po-detail-container"},Ke={class:"po-progress-section"},Ye={class:"info-item"},Ge={class:"value"},Je={class:"info-item"},We={class:"value"},Ze={class:"info-item"},et={class:"value"},tt={class:"info-item"},at={class:"value"},nt={class:"info-item"},ot={class:"value"},it={class:"info-item"},st={class:"value"},lt={class:"info-item"},dt={class:"value"},rt={class:"info-item"},ut={class:"value important"},ct={class:"info-item"},mt={class:"value important"},pt=["onClick"],yt=["onClick"],vt={class:"summary-section"},ft={class:"summary-item"},_t={class:"value"},ht={class:"summary-item"},kt={class:"value important"},It={class:"summary-item"},xt={class:"value"},Pt={class:"summary-item total"},gt={class:"value important"},bt={key:0,style:{"margin-bottom":"16px"}},wt={class:"delivery-expanded-content"},Ct={key:0,class:"other-po-notice"},St={class:"notice-text"},Nt={class:"delivery-expanded-content"},Rt={key:0,class:"other-po-notice"},Dt={class:"notice-text"},At={class:"delivery-expanded-content"},Mt={key:0,class:"other-po-notice"},Bt={class:"notice-text"},qt={class:"ant-upload-drag-icon"},Tt={class:"comment-header"},Qt={class:"comment-user"},$t={class:"comment-time"},Ot={class:"comment-content"},zt={class:"log-time"},Et={class:"log-content"},Lt={class:"log-user"},Ft={key:0,class:"log-detail"},Ht={class:"bottom-actions"},Vt={__name:"poDetail",setup(Ut){const V=Le(),Q=He(),z=k(V.params.id||V.query.id),E=k(!1),R=k(""),D=k([]),A=k([]),M=k([]),B=k(!1),q=k([]),U=k([{id:"1",name:"王五",phone:"***********",region:"北京市海淀区",address:"科技园区888号智能制造中心3号楼",isDefault:!0},{id:"2",name:"李四",phone:"13800138000",region:"上海市浦东新区",address:"张江高科技园区999号创新大厦5楼",isDefault:!1},{id:"3",name:"张三",phone:"13700137000",region:"深圳市南山区",address:"科技园南区软件园A栋2楼",isDefault:!1}]),ae=[{title:"联系人",dataIndex:"name",key:"name"},{title:"联系电话",dataIndex:"phone",key:"phone"},{title:"地区",dataIndex:"region",key:"region"},{title:"详细地址",dataIndex:"address",key:"address"},{title:"默认",dataIndex:"isDefault",key:"isDefault",customRender:({text:e})=>e?"✓":""}],i=k({id:"",poNo:"",status:"draft",invoicingStatus:"invoicing",paymentStatus:"partially_paid",createTime:"",expectedDeliveryDate:"2023-11-15",purchaser:"张三",purchaseGroup:"机械装备组",contactPerson:"李四",contactPhone:"13800138000",paymentMethod:"现金（电汇）",paymentTerms:"月结，账期15日",subtotalAmount:185e3,tax:16650,shippingFee:2500,totalAmount:204150,residue:5e5,receiverName:"王五",receiverPhone:"***********",receiverRegion:"北京市海淀区",receiverAddress:"科技园区888号智能制造中心3号楼",receivingTimeRequirement:"工作日9:00-17:00，提前2小时电话通知",remark:"物品易碎，请妥善包装",deliveryMethod:"快递",deliveryService:"包含安装调试服务",packagingRequirements:"防潮、防震包装",invoiceTitle:"XX科技有限公司",taxId:"91110108MA01R1XNXX",invoiceRegisteredAddress:"北京市海淀区中关村软件园XX号",invoicePhone:"010-********",invoiceBankName:"中国工商银行北京海淀支行",invoiceBankAccount:"0200049619200088888",items:[{id:"1",key:"1",name:"伺服电机",model:"SM2000",brand:"ABB",category:"传动系统",rfqNo:"RFQ-2023-0001",quantity:5,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,expectedArrivalDate:"2023-11-10",unitPrice:12e3,totalPrice:6e4,status:"draft"},{id:"2",key:"2",name:"工业控制器",model:"IC5000",brand:"Siemens",category:"控制系统",rfqNo:"RFQ-2023-0001",quantity:2,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,expectedArrivalDate:"2023-11-12",unitPrice:35e3,totalPrice:7e4,status:"draft"},{id:"3",key:"3",name:"传感器",model:"S3000",brand:"Honeywell",category:"传感设备",rfqNo:"RFQ-2023-0001",quantity:20,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,expectedArrivalDate:"2023-10-30",unitPrice:2500,totalPrice:5e4,status:"draft"},{id:"4",key:"4",name:"液压阀门",model:"HV200",brand:"Parker",category:"液压系统",rfqNo:"RFQ-2023-0001",quantity:5,shippedQuantity:0,receivedQuantity:0,cancelledQuantity:0,expectedArrivalDate:"2023-11-05",unitPrice:1e3,totalPrice:5e3,status:"draft"}],logisticsRecords:[],attachments:[{id:"1",name:"合同文档.pdf",type:"pdf",size:"2.5MB",uploadTime:"2023-10-15 10:30:00",uploadUser:"张三"},{id:"2",name:"技术规格说明.docx",type:"docx",size:"1.8MB",uploadTime:"2023-10-15 11:20:00",uploadUser:"张三"}],comments:[{id:"1",userName:"张三",time:"2023-10-15 10:45:00",content:"已与供应商确认交货时间"},{id:"2",userName:"李四",time:"2023-10-16 14:30:00",content:"请注意控制器的型号需要与之前的设备兼容"}],operationLogs:[{id:"1",userName:"张三",time:"2023-10-15 09:30:00",action:"创建了采购单"},{id:"2",userName:"王经理",time:"2023-10-15 14:20:00",action:"审批通过了采购单"},{id:"3",userName:"张三",time:"2023-10-16 09:15:00",action:"发送采购单给供应商"},{id:"4",userName:"系统",time:"2023-10-18 14:30:00",action:"更新了物流状态",detail:"供应商已发货"}]}),ne=[{title:"对账单号",dataIndex:"statementNo",key:"statementNo",customRender:({text:e,record:t})=>d("div",{},[d("a",{onClick:()=>Y(t.statementNo),style:{color:"#f5222d",cursor:"pointer"}},e),t.hasOtherPoItems&&d("a-tooltip",{title:"混合对账"},{default:()=>d(H,{style:{color:"#faad14",marginLeft:"8px",fontSize:"16px",cursor:"pointer"}})})])},{title:"创建时间",dataIndex:"createTime",key:"createTime",customRender:({text:e})=>b(e)},{title:"对账周期",dataIndex:"period",key:"period"},{title:"总金额(元)",dataIndex:"totalAmount",key:"totalAmount",customRender:({text:e})=>m(e)},{title:"状态",dataIndex:"status",key:"status",customRender:({text:e})=>{var r,p;const t={pending:{text:"待核对",color:"orange"},confirmed:{text:"已核对",color:"blue"},paid:{text:"已付款",color:"green"}};return d("a-tag",{color:(r=t[e])==null?void 0:r.color},((p=t[e])==null?void 0:p.text)||e)}},{title:"开票状态",dataIndex:"invoiceStatus",key:"invoiceStatus",customRender:({text:e})=>{var r,p;const t={pending:{text:"待开票",color:"orange"},invoicing:{text:"开票中",color:"blue"},invoiced:{text:"已开票",color:"green"}};return d("a-tag",{color:(r=t[e])==null?void 0:r.color},((p=t[e])==null?void 0:p.text)||e)}},{title:"本采购单物料情况",key:"currentPoSummary",customRender:({record:e})=>d("div",{class:"po-material-summary"},[d("div",{class:"summary-line"},`种类：${e.currentPoItemCount}/${e.totalItemCount}`),d("div",{class:"summary-line"},`金额：¥${m(e.currentPoAmount)}`),e.hasOtherPoItems&&d("div",{class:"other-po-hint"},`另含其他采购单物料${e.otherPoItemCount}种`)])}],oe=[{title:"物料名称",dataIndex:"name",key:"name",width:120},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"型号",dataIndex:"model",key:"model",width:120},{title:"参考品牌",dataIndex:"referenceBrand",key:"referenceBrand",width:100},{title:"参考型号",dataIndex:"referenceModel",key:"referenceModel",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"单价(元)",dataIndex:"unitPrice",key:"unitPrice",width:100,customRender:({text:e})=>m(e)},{title:"总价(元)",dataIndex:"totalPrice",key:"totalPrice",width:120,customRender:({text:e})=>m(e)},{title:"收货日期",dataIndex:"receivedDate",key:"receivedDate",width:120},{title:"送货单号",dataIndex:"deliveryNo",key:"deliveryNo",width:120,customRender:({text:e})=>d("a",{onClick:()=>K(e),style:{color:"#f5222d",cursor:"pointer"}},e)}],$=k({statements:[{id:"stmt-1",key:"stmt-1",statementNo:"STMT-2023-001",createTime:"2023-11-01 09:30:00",period:"2023年10月",totalAmount:185e3,status:"confirmed",invoiceStatus:"invoiced",currentPoItemCount:3,currentPoAmount:135e3,totalItemCount:8,currentPoItems:[{id:"stmt-item1",name:"传感器",brand:"Honeywell",model:"S3000",referenceBrand:"Honeywell",referenceModel:"S3000",quantity:10,unitPrice:2500,totalPrice:25e3,receivedDate:"2023-10-20",deliveryNo:"DEL-2023-0001"},{id:"stmt-item2",name:"液压阀门",brand:"Parker",model:"HV200",referenceBrand:"Parker",referenceModel:"HV200",quantity:5,unitPrice:1e3,totalPrice:5e3,receivedDate:"2023-10-20",deliveryNo:"DEL-2023-0001"},{id:"stmt-item3",name:"伺服电机",brand:"ABB",model:"SM2000",referenceBrand:"ABB",referenceModel:"SM2000",quantity:5,unitPrice:12e3,totalPrice:6e4,receivedDate:"2023-10-25",deliveryNo:"DEL-2023-0003"}],hasOtherPoItems:!0,otherPoItemCount:5,otherPoAmount:5e4},{id:"stmt-2",key:"stmt-2",statementNo:"STMT-2023-002",createTime:"2023-11-15 14:20:00",period:"2023年11月",totalAmount:95e3,status:"pending",invoiceStatus:"pending",currentPoItemCount:1,currentPoAmount:45e3,totalItemCount:3,currentPoItems:[{id:"stmt-item4",name:"工业控制器",brand:"Siemens",model:"IC5000",referenceBrand:"Siemens",referenceModel:"IC5000",quantity:1,unitPrice:35e3,totalPrice:35e3,receivedDate:"2023-11-10",deliveryNo:"DEL-2023-0002"}],hasOtherPoItems:!0,otherPoItemCount:2,otherPoAmount:5e4}],payments:[{id:"pay-1",key:"pay-1",paymentNo:"PAY-2023-0001",status:"paid",totalDue:185e3,totalPaid:185e3,totalPending:0,statementNo:"STMT-2023-001",createTime:"2023-11-01 14:30:00",paymentCompletedTime:"2023-11-02 16:45:00",paymentOverdueTime:null,currentPoItemCount:3,currentPoAmount:135e3,totalItemCount:8,currentPoItems:[{id:"pay-item1",name:"传感器",brand:"Honeywell",model:"S3000",referenceBrand:"Honeywell",referenceModel:"S3000",quantity:10,unitPrice:2500,totalPrice:25e3},{id:"pay-item2",name:"液压阀门",brand:"Parker",model:"HV200",referenceBrand:"Parker",referenceModel:"HV200",quantity:5,unitPrice:1e3,totalPrice:5e3},{id:"pay-item3",name:"伺服电机",brand:"ABB",model:"SM2000",referenceBrand:"ABB",referenceModel:"SM2000",quantity:5,unitPrice:12e3,totalPrice:6e4}],hasOtherPoItems:!0,otherPoItemCount:5,otherPoAmount:5e4},{id:"pay-2",key:"pay-2",paymentNo:"PAY-2023-0002",status:"pending",totalDue:95e3,totalPaid:0,totalPending:95e3,statementNo:"STMT-2023-002",createTime:"2023-11-15 09:15:00",paymentCompletedTime:null,paymentOverdueTime:"2023-12-15 23:59:59",currentPoItemCount:1,currentPoAmount:35e3,totalItemCount:3,currentPoItems:[{id:"pay-item4",name:"工业控制器",brand:"Siemens",model:"IC5000",referenceBrand:"Siemens",referenceModel:"IC5000",quantity:1,unitPrice:35e3,totalPrice:35e3}],hasOtherPoItems:!0,otherPoItemCount:2,otherPoAmount:6e4}],deliveries:[{id:"1",key:"1",docNo:"DEL-2023-0001",status:"delivered",shippingDate:"2023-10-18 09:30:00",totalItemCount:5,totalQuantity:25,currentPoItemCount:2,currentPoQuantity:15,logistics:[{provider:"顺丰速运",trackingNo:"SF********90"},{provider:"中通快递",trackingNo:"ZT0987654321"}],currentPoItems:[{id:"item1",name:"传感器",brand:"Honeywell",model:"S3000",quantity:10,referenceBrand:"Honeywell",referenceModel:"S3000",poItemId:"3"},{id:"item2",name:"液压阀门",brand:"Parker",model:"HV200",quantity:5,referenceBrand:"Parker",referenceModel:"HV200",poItemId:"4"}],hasOtherPoItems:!0,otherPoItemCount:3,otherPoQuantity:10},{id:"2",key:"2",docNo:"DEL-2023-0002",status:"preparing",shippingDate:"2023-10-22 10:00:00",totalItemCount:3,totalQuantity:3,currentPoItemCount:2,currentPoQuantity:3,logistics:[],currentPoItems:[{id:"item3",name:"伺服电机",brand:"ABB",model:"SM2000",quantity:2,referenceBrand:"ABB",referenceModel:"SM2000",poItemId:"1"},{id:"item4",name:"工业控制器",brand:"Siemens",model:"IC5000",quantity:1,referenceBrand:"Siemens",referenceModel:"IC5000",poItemId:"2"}],hasOtherPoItems:!0,otherPoItemCount:1,otherPoQuantity:5},{id:"3",key:"3",docNo:"DEL-2023-0003",status:"shipped",shippingDate:"2023-10-25 14:30:00",totalItemCount:1,totalQuantity:3,currentPoItemCount:1,currentPoQuantity:3,logistics:[{provider:"京东物流",trackingNo:"JD8888888888"}],currentPoItems:[{id:"item5",name:"伺服电机",brand:"ABB",model:"SM2000",quantity:3,referenceBrand:"ABB",referenceModel:"SM2000",poItemId:"1"}],hasOtherPoItems:!1,otherPoItemCount:0,otherPoQuantity:0}],returns:[],invoices:[{id:"1",key:"1",invoiceNo:"INV-2023-0001",type:"增值税专用发票",amount:204150,status:"issued",issueDate:"2023-10-25 11:00:00",downloadUrl:"/api/download/invoice/INV-2023-0001"}]}),ie=k([{title:"创建草稿",time:"2023-10-15 09:30:00"},{title:"提交订单",time:null},{title:"订单确认",time:null},{title:"完成订单",time:null}]),se=N(()=>({draft:0,pending:1,in_progress:2,completed:3,archived:4})[i.value.status]||0),le=[{title:"物料名称",dataIndex:"name",key:"name",width:180},{title:"型号",dataIndex:"model",key:"model",width:150},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"分类",dataIndex:"category",key:"category",width:120},{title:"来源询价单",dataIndex:"rfqNo",key:"rfqNo",width:150},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"已发货数量",dataIndex:"shippedQuantity",key:"shippedQuantity",width:110},{title:"已收货数量",dataIndex:"receivedQuantity",key:"receivedQuantity",width:110},{title:"已取消数量",dataIndex:"cancelledQuantity",key:"cancelledQuantity",width:110},{title:"物流状态",dataIndex:"logisticsStatus",key:"logisticsStatus",width:100},{title:"财务状态",dataIndex:"financialStatus",key:"financialStatus",width:100},{title:"单价（¥）",dataIndex:"unitPrice",key:"unitPrice",width:100,customRender:({text:e})=>m(e)},{title:"总价（¥）",dataIndex:"totalPrice",key:"totalPrice",width:120,customRender:({text:e})=>m(e)},{title:"预计到货日期",dataIndex:"expectedArrivalTime",key:"expectedArrivalTime",width:150},{title:"操作（⚠️看需求）",dataIndex:"actions",key:"actions",width:180,fixed:"right"}],de=[{title:"付款单号",dataIndex:"paymentNo",key:"paymentNo",customRender:({text:e,record:t})=>d("div",{},[d("a",{onClick:()=>De(t.paymentNo),style:{color:"#f5222d",cursor:"pointer"}},e),t.hasOtherPoItems&&d("a-tooltip",{title:"混合付款"},{default:()=>d(H,{style:{color:"#faad14",marginLeft:"8px",fontSize:"16px",cursor:"pointer"}})})])},{title:"状态",dataIndex:"status",key:"status",customRender:({text:e})=>{var r,p;const t={pending:{text:"待支付",color:"orange"},paid:{text:"已支付",color:"green"},cancelled:{text:"已取消",color:"red"},overdue:{text:"逾期",color:"volcano"}};return d("a-tag",{color:(r=t[e])==null?void 0:r.color},((p=t[e])==null?void 0:p.text)||e)}},{title:"金额情况",key:"amountSummary",customRender:({record:e})=>d("div",{class:"po-material-summary"},[d("div",{class:"summary-line"},`应付：¥${m(e.totalDue)}`),d("div",{class:"summary-line"},`实付：¥${m(e.totalPaid)}`),e.totalPending>0&&d("div",{class:"summary-line",style:{color:"#f5222d"}},`待付：¥${m(e.totalPending)}`)])},{title:"所属对账单",dataIndex:"statementNo",key:"statementNo",customRender:({text:e})=>d("a",{onClick:()=>Y(e),style:{color:"#f5222d",cursor:"pointer"}},e)},{title:"创建时间",dataIndex:"createTime",key:"createTime",customRender:({text:e})=>b(e)},{title:"付款完成时间",dataIndex:"paymentCompletedTime",key:"paymentCompletedTime",customRender:({text:e})=>e?b(e):"-"},{title:"付款逾期时间",dataIndex:"paymentOverdueTime",key:"paymentOverdueTime",customRender:({text:e,record:t})=>{if(!e)return"-";const r=new Date(e)<new Date;return d("span",{style:{color:r?"#f5222d":"#666"}},b(e))}},{title:"本采购单物料情况",key:"currentPoSummary",customRender:({record:e})=>d("div",{class:"po-material-summary"},[d("div",{class:"summary-line"},`种类：${e.currentPoItemCount}/${e.totalItemCount}`),d("div",{class:"summary-line"},`金额：¥${m(e.currentPoAmount)}`),e.hasOtherPoItems&&d("div",{class:"other-po-hint"},`另含其他采购单物料${e.otherPoItemCount}种`)])}],re=[{title:"物料名称",dataIndex:"name",key:"name",width:120},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"型号",dataIndex:"model",key:"model",width:120},{title:"参考品牌",dataIndex:"referenceBrand",key:"referenceBrand",width:100},{title:"参考型号",dataIndex:"referenceModel",key:"referenceModel",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"单价(元)",dataIndex:"unitPrice",key:"unitPrice",width:100,customRender:({text:e})=>m(e)},{title:"总价(元)",dataIndex:"totalPrice",key:"totalPrice",width:120,customRender:({text:e})=>m(e)}],ue=[{title:"送货单号",dataIndex:"docNo",key:"docNo",customRender:({text:e,record:t})=>d("div",{},[d("a",{onClick:()=>K(t.docNo),style:{color:"#f5222d",cursor:"pointer"}},e),t.hasOtherPoItems&&d("a-tooltip",{title:"混合送货"},{default:()=>d(H,{style:{color:"#faad14",marginLeft:"8px",fontSize:"16px",cursor:"pointer"}})})])},{title:"状态",dataIndex:"status",key:"status",customRender:({text:e})=>{var r,p;const t={preparing:{text:"准备中",color:"blue"},shipped:{text:"已发货",color:"purple"},in_transit:{text:"运输中",color:"orange"},delivered:{text:"已送达",color:"green"}};return d("a-tag",{color:(r=t[e])==null?void 0:r.color},((p=t[e])==null?void 0:p.text)||e)}},{title:"发货时间",dataIndex:"shippingDate",key:"shippingDate",customRender:({text:e})=>b(e)},{title:"物流单数量",key:"logisticsCount",customRender:({record:e})=>{var t;return`${((t=e.logistics)==null?void 0:t.length)||0}个`}},{title:"本采购单物料情况",key:"currentPoSummary",customRender:({record:e})=>d("div",{class:"po-material-summary"},[d("div",{class:"summary-line"},`种类：${e.currentPoItemCount}/${e.totalItemCount}`),d("div",{class:"summary-line"},`数量：${e.currentPoQuantity}件`),e.hasOtherPoItems&&d("div",{class:"other-po-hint"},`另含其他采购单物料${e.otherPoItemCount}种`)])}],ce=[{title:"物料名称",dataIndex:"name",key:"name",width:120},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"型号",dataIndex:"model",key:"model",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"参考品牌",dataIndex:"referenceBrand",key:"referenceBrand",width:100},{title:"参考型号",dataIndex:"referenceModel",key:"referenceModel",width:120}],me=[{title:"单据编号",dataIndex:"docNo",key:"docNo"},{title:"物品",dataIndex:"items",key:"items"},{title:"退货原因",dataIndex:"reason",key:"reason"},{title:"退货数量",dataIndex:"quantity",key:"quantity"},{title:"退款金额",dataIndex:"amount",key:"amount",customRender:({text:e})=>m(e)},{title:"状态",dataIndex:"status",key:"status"},{title:"申请日期",dataIndex:"applyDate",key:"applyDate"},{title:"处理日期",dataIndex:"processDate",key:"processDate"},{title:"操作",key:"action",customRender:()=>d("a",{},"查看")}],pe=[{title:"文件名",dataIndex:"name",key:"name"},{title:"类型",dataIndex:"type",key:"type"},{title:"大小",dataIndex:"size",key:"size"},{title:"上传时间",dataIndex:"uploadTime",key:"uploadTime"},{title:"上传人",dataIndex:"uploadUser",key:"uploadUser"},{title:"操作",key:"action",customRender:()=>d("a-space",{},[d("a",{},"预览"),d("a-divider",{type:"vertical"}),d("a",{},"下载")])}];N(()=>["draft","pending"].includes(i.value.status)),N(()=>i.value.status==="draft");const X=N(()=>!["cancelled","completed","archived"].includes(i.value.status)),ye=N(()=>!0),ve=N(()=>!0),fe=()=>{Q.go(-1)},j=e=>({draft:"草稿",pending:"待确认",in_progress:"执行中",completed:"已完成",cancelled:"已取消",cancelling:"取消中",archived:"已归档",exception:"异常待处理"})[e]||e,_e=e=>({draft:"blue",pending:"orange",in_progress:"purple",completed:"green",cancelled:"red",cancelling:"pink",archived:"gray",exception:"volcano"})[e]||"default",b=e=>e||"",m=e=>e==null?"0.00":e.toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),he=()=>i.value.items.reduce((e,t)=>e+t.quantity,0),ke=e=>{e.file.status==="done"?h.success(`${e.file.name} 上传成功`):e.file.status==="error"&&h.error(`${e.file.name} 上传失败`)},Ie=()=>{if(!R.value.trim()){h.warning("请输入评论内容");return}i.value.comments.push({id:Date.now().toString(),userName:"当前用户",time:new Date().toLocaleString("zh-CN"),content:R.value}),h.success("评论已添加"),R.value=""},xe=(e,t)=>{e.totalPrice=e.quantity*e.unitPrice;const r=i.value.items.reduce((p,T)=>p+T.totalPrice,0);i.value.subtotalAmount=r,i.value.totalAmount=r+i.value.shippingFee,h.success(`已更新${e.name}的数量为${e.quantity}`)},Pe=(e,t)=>{i.value.items.splice(t,1);const r=i.value.items.reduce((p,T)=>p+T.totalPrice,0);i.value.subtotalAmount=r,i.value.totalAmount=r+i.value.shippingFee,h.success(`已删除物料: ${e.name}`)},ge=e=>{i.value.status!=="draft"&&(e.status="cancelled",e.cancelledQuantity=e.quantity,h.success(`已取消物料: ${e.name}`))},be=()=>{B.value=!0},we=e=>{q.value=e},Ce=()=>{if(q.value.length===0){h.warning("请选择收货信息");return}const e=U.value.find(t=>t.id===q.value[0]);e&&(i.value.receiverName=e.name,i.value.receiverPhone=e.phone,i.value.receiverRegion=e.region,i.value.receiverAddress=e.address,h.success(`已选择收货信息: ${e.name}`)),B.value=!1,q.value=[]},K=e=>{Q.push({path:`/purchase/dn/detail/${e}`,query:{from:"po-detail",poId:z.value}}),h.info(`跳转到送货单详情: ${e}`)},Se=(e,t)=>{if(e)D.value.includes(t.id)||D.value.push(t.id);else{const r=D.value.indexOf(t.id);r>-1&&D.value.splice(r,1)}},Ne=(e,t)=>{if(e)A.value.includes(t.id)||A.value.push(t.id);else{const r=A.value.indexOf(t.id);r>-1&&A.value.splice(r,1)}},Re=(e,t)=>{if(e)M.value.includes(t.id)||M.value.push(t.id);else{const r=M.value.indexOf(t.id);r>-1&&M.value.splice(r,1)}},Y=e=>{Q.push({path:`/purchase/statement/detail/${e}`,query:{from:"po-detail",poId:z.value}}),h.info(`跳转到对账单详情: ${e}`)},De=e=>{Q.push({path:`/purchase/payment/detail/${e}`,query:{from:"po-detail",poId:z.value}}),h.info(`跳转到付款单详情: ${e}`)},Ae=async()=>{E.value=!0;try{await new Promise(e=>setTimeout(e,500)),E.value=!1}catch{h.error("获取订单详情失败"),E.value=!1}};return Fe(()=>{Ae()}),(e,t)=>{const r=c("a-button"),p=c("a-space"),T=c("a-page-header"),Me=c("a-step"),Be=c("a-steps"),S=c("a-card"),P=c("a-col"),qe=c("a-tag"),G=c("a-row"),Te=c("a-input-number"),g=c("a-table"),x=c("a-descriptions-item"),J=c("a-descriptions"),w=c("a-tab-pane"),L=c("a-tabs"),Qe=c("a-upload-dragger"),$e=c("a-empty"),W=c("a-timeline-item"),Z=c("a-timeline"),Oe=c("a-textarea"),ee=c("a-form-item"),ze=c("a-form"),Ee=c("a-modal");return u(),_("div",je,[n(T,{title:"订单详情 - "+i.value.poNo,"sub-title":"状态："+j(i.value.status),onBack:fe},{extra:a(()=>[n(p,null,{default:a(()=>[X.value?(u(),I(r,{key:0},{default:a(()=>t[3]||(t[3]=[y("取消订单（⚠️见需求）")])),_:1})):f("",!0)]),_:1})]),_:1},8,["title","sub-title"]),o("div",Ke,[n(S,{title:"订单流程"},{default:a(()=>[n(Be,{current:se.value,size:"small"},{default:a(()=>[(u(!0),_(C,null,F(ie.value,(l,v)=>(u(),I(Me,{key:v,title:l.title},{description:a(()=>[o("div",null,s(l.time?b(l.time):"未开始"),1),o("div",null,s(l.operator||""),1)]),_:2},1032,["title"]))),128))]),_:1},8,["current"])]),_:1})]),n(S,{title:"基本信息",class:"detail-card"},{default:a(()=>[n(G,{gutter:24},{default:a(()=>[n(P,{span:8},{default:a(()=>[o("div",Ye,[t[4]||(t[4]=o("span",{class:"label"},"订单号：",-1)),o("span",Ge,s(i.value.poNo),1)])]),_:1}),n(P,{span:8},{default:a(()=>[o("div",Je,[t[5]||(t[5]=o("span",{class:"label"},"订单状态：",-1)),o("span",We,[n(qe,{color:_e(i.value.status)},{default:a(()=>[y(s(j(i.value.status)),1)]),_:1},8,["color"])])])]),_:1}),n(P,{span:8},{default:a(()=>[o("div",Ze,[t[6]||(t[6]=o("span",{class:"label"},"下单时间：",-1)),o("span",et,s(b(i.value.createTime)),1)])]),_:1}),n(P,{span:8},{default:a(()=>[o("div",tt,[t[7]||(t[7]=o("span",{class:"label"},"采购员：",-1)),o("span",at,s(i.value.contactPerson),1)])]),_:1}),n(P,{span:8},{default:a(()=>[o("div",nt,[t[8]||(t[8]=o("span",{class:"label"},"联系电话：",-1)),o("span",ot,s(i.value.contactPhone),1)])]),_:1}),n(P,{span:8},{default:a(()=>[o("div",it,[t[9]||(t[9]=o("span",{class:"label"},"付款方式：",-1)),o("span",st,s(i.value.paymentMethod),1)])]),_:1}),n(P,{span:8},{default:a(()=>[o("div",lt,[t[10]||(t[10]=o("span",{class:"label"},"付款条件：",-1)),o("span",dt,s(i.value.paymentTerms),1)])]),_:1}),n(P,{span:8},{default:a(()=>[o("div",rt,[t[11]||(t[11]=o("span",{class:"label"},"总金额：",-1)),o("span",ut,"¥"+s(m(i.value.totalAmount)),1)])]),_:1}),n(P,{span:8},{default:a(()=>[o("div",ct,[t[12]||(t[12]=o("span",{class:"label"},"当前剩余账期额度：",-1)),o("span",mt,"¥"+s(m(i.value.residue)),1)])]),_:1})]),_:1})]),_:1}),n(S,{title:"物料明细",class:"detail-card"},{default:a(()=>[n(g,{columns:le,"data-source":i.value.items,pagination:!1,size:"middle",scroll:{x:1500}},{bodyCell:a(({column:l,record:v,index:te})=>[l.dataIndex==="quantity"&&i.value.status==="draft"?(u(),I(Te,{key:0,value:v.quantity,"onUpdate:value":O=>v.quantity=O,min:1,onChange:O=>xe(v,te),style:{width:"100%"}},null,8,["value","onUpdate:value","onChange"])):l.dataIndex==="quantity"?(u(),_(C,{key:1},[y(s(v.quantity),1)],64)):f("",!0),l.dataIndex==="shippedQuantity"?(u(),_(C,{key:2},[y(s(i.value.status==="draft"?"-":v.shippedQuantity),1)],64)):f("",!0),l.dataIndex==="receivedQuantity"?(u(),_(C,{key:3},[y(s(i.value.status==="draft"?"-":v.receivedQuantity),1)],64)):f("",!0),l.dataIndex==="cancelledQuantity"?(u(),_(C,{key:4},[y(s(i.value.status==="draft"?"-":v.cancelledQuantity),1)],64)):f("",!0),l.dataIndex==="actions"&&i.value.status==="draft"?(u(),I(p,{key:5},{default:a(()=>[o("a",{onClick:O=>Pe(v,te),style:{color:"#ff4d4f"}},"删除",8,pt)]),_:2},1024)):l.dataIndex==="actions"?(u(),I(p,{key:6},{default:a(()=>[o("a",{onClick:O=>ge(v)},"取消",8,yt)]),_:2},1024)):f("",!0)]),_:1},8,["data-source"]),o("div",vt,[n(G,{justify:"end"},{default:a(()=>[n(P,{span:8},{default:a(()=>[o("div",ft,[t[13]||(t[13]=o("span",{class:"label"},"物料总数：",-1)),o("span",_t,s(he())+" 件",1)]),o("div",ht,[t[14]||(t[14]=o("span",{class:"label"},"物料总价：",-1)),o("span",kt,"¥"+s(m(i.value.subtotalAmount)),1)]),o("div",It,[t[15]||(t[15]=o("span",{class:"label"},"运费：",-1)),o("span",xt,"¥"+s(m(i.value.shippingFee)),1)]),o("div",Pt,[t[16]||(t[16]=o("span",{class:"label"},"应付总额：",-1)),o("span",gt,"¥"+s(m(i.value.totalAmount)),1)])]),_:1})]),_:1})])]),_:1}),n(S,{title:"收货与开票信息",class:"detail-card"},{default:a(()=>[n(L,{"default-active-key":"1"},{default:a(()=>[n(w,{key:"1",tab:"收货信息"},{default:a(()=>[i.value.status==="draft"?(u(),_("div",bt,[n(r,{type:"primary",onClick:be},{default:a(()=>t[17]||(t[17]=[y("选择收货信息")])),_:1})])):f("",!0),n(J,{column:2,bordered:""},{default:a(()=>[n(x,{label:"联系人"},{default:a(()=>[y(s(i.value.receiverName),1)]),_:1}),n(x,{label:"手机号"},{default:a(()=>[y(s(i.value.receiverPhone),1)]),_:1}),n(x,{label:"地区",span:2},{default:a(()=>[y(s(i.value.receiverRegion),1)]),_:1}),n(x,{label:"详细地址",span:2},{default:a(()=>[y(s(i.value.receiverAddress),1)]),_:1}),n(x,{label:"备注",span:2},{default:a(()=>[y(s(i.value.remark),1)]),_:1})]),_:1})]),_:1}),n(w,{key:"2",tab:"开票信息"},{default:a(()=>[n(J,{column:2,bordered:""},{default:a(()=>[n(x,{label:"发票抬头"},{default:a(()=>[y(s(i.value.invoiceTitle),1)]),_:1}),n(x,{label:"税号"},{default:a(()=>[y(s(i.value.taxId),1)]),_:1}),n(x,{label:"注册地址",span:2},{default:a(()=>[y(s(i.value.invoiceRegisteredAddress),1)]),_:1}),n(x,{label:"电话"},{default:a(()=>[y(s(i.value.invoicePhone),1)]),_:1}),n(x,{label:"开户行"},{default:a(()=>[y(s(i.value.invoiceBankName),1)]),_:1}),n(x,{label:"银行账户",span:2},{default:a(()=>[y(s(i.value.invoiceBankAccount),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),n(S,{title:"相关单据",class:"detail-card"},{default:a(()=>[n(L,{"default-active-key":"statement"},{default:a(()=>[n(w,{key:"2",tab:"送货单"},{default:a(()=>[n(g,{columns:ue,"data-source":$.value.deliveries,pagination:{pageSize:5},size:"small","expand-row-by-click":!0,"expanded-row-keys":D.value,onExpand:Se},{expandedRowRender:a(({record:l})=>[o("div",wt,[t[18]||(t[18]=o("div",{class:"expanded-title"},"本采购单物料明细",-1)),n(g,{columns:ce,"data-source":l.currentPoItems,pagination:!1,size:"small","show-header":!0},null,8,["data-source"]),l.hasOtherPoItems?(u(),_("div",Ct,[o("div",St," 💡 此送货单还包含其他采购单的物料："+s(l.otherPoItemCount)+"种，共"+s(l.otherPoQuantity)+"件 ",1)])):f("",!0)])]),_:1},8,["data-source","expanded-row-keys"])]),_:1}),n(w,{key:"3",tab:"退货单"},{default:a(()=>[n(g,{columns:me,"data-source":$.value.returns,pagination:{pageSize:5},size:"small"},null,8,["data-source"])]),_:1}),n(w,{key:"statement",tab:"对账单"},{default:a(()=>[n(g,{columns:ne,"data-source":$.value.statements,pagination:{pageSize:5},size:"small","expand-row-by-click":!0,"expanded-row-keys":A.value,onExpand:Ne},{expandedRowRender:a(({record:l})=>[o("div",Nt,[t[19]||(t[19]=o("div",{class:"expanded-title"},"本采购单物料明细",-1)),n(g,{columns:oe,"data-source":l.currentPoItems,pagination:!1,size:"small","show-header":!0,scroll:{x:1200}},null,8,["data-source"]),l.hasOtherPoItems?(u(),_("div",Rt,[o("div",Dt," 💡 此对账单还包含其他采购单的物料："+s(l.otherPoItemCount)+"种，金额¥"+s(m(l.otherPoAmount)),1)])):f("",!0)])]),_:1},8,["data-source","expanded-row-keys"])]),_:1}),n(w,{key:"1",tab:"付款单"},{default:a(()=>[n(g,{columns:de,"data-source":$.value.payments,pagination:{pageSize:5},size:"small","expand-row-by-click":!0,"expanded-row-keys":M.value,onExpand:Re},{expandedRowRender:a(({record:l})=>[o("div",At,[t[20]||(t[20]=o("div",{class:"expanded-title"},"本采购单物料明细",-1)),n(g,{columns:re,"data-source":l.currentPoItems,pagination:!1,size:"small","show-header":!0,scroll:{x:1e3}},null,8,["data-source"]),l.hasOtherPoItems?(u(),_("div",Mt,[o("div",Bt," 💡 此付款单还包含其他采购单的物料："+s(l.otherPoItemCount)+"种，金额¥"+s(m(l.otherPoAmount)),1)])):f("",!0)])]),_:1},8,["data-source","expanded-row-keys"])]),_:1})]),_:1})]),_:1}),n(S,{title:"附件与备注",class:"detail-card"},{default:a(()=>[n(L,{"default-active-key":"1"},{default:a(()=>[n(w,{key:"1",tab:"附件资料"},{default:a(()=>[ye.value?(u(),I(Qe,{key:0,name:"file",multiple:!0,action:"/api/upload",onChange:ke},{default:a(()=>[o("p",qt,[n(Ve(Xe))]),t[21]||(t[21]=o("p",{class:"ant-upload-text"},"点击或拖拽文件到此区域上传",-1)),t[22]||(t[22]=o("p",{class:"ant-upload-hint"},"支持单个或批量上传。支持 PDF, Word, Excel, 图片等格式文件",-1))]),_:1})):f("",!0),i.value.attachments&&i.value.attachments.length>0?(u(),I(g,{key:1,columns:pe,"data-source":i.value.attachments,pagination:!1,size:"small"},null,8,["data-source"])):(u(),I($e,{key:2,description:"暂无附件"}))]),_:1}),n(w,{key:"2",tab:"沟通记录"},{default:a(()=>[n(Z,null,{default:a(()=>[(u(!0),_(C,null,F(i.value.comments,(l,v)=>(u(),I(W,{key:v},{default:a(()=>[o("div",Tt,[o("span",Qt,s(l.userName),1),o("span",$t,s(b(l.time)),1)]),o("div",Ot,s(l.content),1)]),_:2},1024))),128))]),_:1}),ve.value?(u(),I(ze,{key:0,layout:"inline",class:"comment-form"},{default:a(()=>[n(ee,{style:{flex:"1"}},{default:a(()=>[n(Oe,{value:R.value,"onUpdate:value":t[0]||(t[0]=l=>R.value=l),placeholder:"添加备注...",rows:2},null,8,["value"])]),_:1}),n(ee,null,{default:a(()=>[n(r,{type:"primary",onClick:Ie},{default:a(()=>t[23]||(t[23]=[y("发送")])),_:1})]),_:1})]),_:1})):f("",!0)]),_:1}),n(w,{key:"3",tab:"操作历史"},{default:a(()=>[n(Z,null,{default:a(()=>[(u(!0),_(C,null,F(i.value.operationLogs,(l,v)=>(u(),I(W,{key:v},{default:a(()=>[o("div",zt,s(b(l.time)),1),o("div",Et,[o("span",Lt,s(l.userName),1),o("span",null,s(l.action),1)]),l.detail?(u(),_("div",Ft,s(l.detail),1)):f("",!0)]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})]),_:1}),o("div",Ht,[n(p,null,{default:a(()=>[X.value?(u(),I(r,{key:0},{default:a(()=>t[24]||(t[24]=[y("取消订单（⚠️见需求）")])),_:1})):f("",!0)]),_:1})]),n(Ee,{visible:B.value,"onUpdate:visible":t[1]||(t[1]=l=>B.value=l),title:"选择收货信息",width:800,onOk:Ce,onCancel:t[2]||(t[2]=l=>B.value=!1)},{default:a(()=>[n(g,{columns:ae,"data-source":U.value,pagination:!1,"row-selection":{type:"radio",selectedRowKeys:q.value,onChange:we},size:"small"},null,8,["data-source","row-selection"])]),_:1},8,["visible"])])}}},Yt=Ue(Vt,[["__scopeId","data-v-3353737a"]]);export{Yt as default};
