import{_ as W}from"./logo-7sOJmgiz.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{r as P,m as Y,b as n,d as e,f as M,F as k,g as l,w as o,h,n as T,o as s,j as m,t as i,k as _}from"./index-B4CbS3Hl.js";const Z={class:"shared-rfq-page"},tt={class:"page-header"},et={class:"header-content"},at={class:"header-actions"},st={class:"main-content"},nt={class:"rfq-info"},ot={class:"info-item"},it={class:"value"},lt={class:"info-item"},dt={class:"value"},rt={class:"info-item"},ut={class:"value"},ct={class:"info-item"},pt={class:"value"},mt={class:"info-item"},_t={class:"value"},yt={class:"info-item"},vt={class:"value"},ft={class:"info-item"},gt={class:"value"},kt={class:"info-item"},ht={class:"value"},xt={class:"info-item"},qt={class:"value important"},bt={class:"material-info"},wt={class:"table-toolbar",style:{"margin-bottom":"16px"}},It={key:2,class:"quote-progress"},Pt={class:"quoted-count"},Ct={class:"rejected-count"},Mt={key:0},Tt={key:1,class:"text-muted"},St={key:2},Dt={style:{margin:"12px 0"}},At={key:0,class:"supplier-blur-overlay"},Ft={class:"blurred-supplier-table"},Nt={class:"login-prompt-overlay"},$t={class:"prompt-content"},Qt={key:0},jt={key:1,class:"text-muted"},Et={key:0},Ot={key:1,class:"text-muted"},Bt={key:0},zt={key:1,class:"text-muted"},Lt={key:0},Vt={key:1,class:"text-muted"},Kt={key:0},Rt={key:1,class:"text-muted"},Gt={key:0},Ut={key:1,class:"text-muted"},Ht={__name:"shared",setup(Jt){const b=P(!1),x=P([]),w=P(!1),N=P({name:"大疆科技",fullName:"深圳市大疆科技有限公司"});P({name:"张三"});const p=P({id:"",rfqNo:"",status:"inProgress",createTime:"",inquiryTime:"",deadline:"",endTime:"",creator:"",contactPhone:"",materialModelCount:0,remark:"",materials:[]}),$=[{title:"物料名称",dataIndex:"name",key:"name",width:180,fixed:"left"},{title:"型号",dataIndex:"model",key:"model",width:180},{title:"品牌",dataIndex:"brand",key:"brand",width:100},{title:"数量",dataIndex:"quantity",key:"quantity",width:80,align:"center"},{title:"询价状态",dataIndex:"status",key:"status",width:100,align:"center"},{title:"期望交期",dataIndex:"expectedDelivery",key:"expectedDelivery",width:100},{title:"接受平替",dataIndex:"acceptAlternative",key:"acceptAlternative",width:100,align:"center"},{title:"报价进度",dataIndex:"quoteProgress",key:"quoteProgress",width:200,align:"center"},{title:"备注",dataIndex:"remark",key:"remark",width:150}],D=[{title:"供应商名称",dataIndex:"name",key:"name",width:150},{title:"报价 (¥)",dataIndex:"price",key:"price",width:100,align:"right"},{title:"总价 (¥)",dataIndex:"totalPrice",key:"totalPrice",width:100,align:"right"},{title:"最小起订量",dataIndex:"minOrderQuantity",key:"minOrderQuantity",width:120,align:"center"},{title:"承诺交期",dataIndex:"promisedDelivery",key:"promisedDelivery",width:100},{title:"有效期",dataIndex:"validityPeriod",key:"validityPeriod",width:100},{title:"报价时间",dataIndex:"quoteTime",key:"quoteTime",width:150},{title:"报价状态",dataIndex:"status",key:"status",width:100,align:"center"},{title:"备注",dataIndex:"remark",key:"remark",width:150}],Q=(a,t)=>{if(a)x.value.push(t.id);else{const u=x.value.indexOf(t.id);u>-1&&x.value.splice(u,1)}j()},j=()=>{w.value=x.value.length===p.value.materials.length},E=()=>{w.value?(x.value=[],w.value=!1):(x.value=p.value.materials.map(a=>a.id),w.value=!0)},S=a=>a||"",O=a=>({notStarted:"未开始",inProgress:"询价中",accepted:"已采纳",expired:"已过期",invalid:"已失效",cancelled:"已取消"})[a]||a,B=a=>({notStarted:"default",inProgress:"blue",accepted:"green",expired:"orange",invalid:"red",cancelled:"red"})[a]||"default",z=a=>({pending:"blue",quoted:"green",rejected:"red",expired:"orange"})[a]||"default",L=a=>({pending:"待报价",quoted:"已报价",rejected:"已拒绝",expired:"已过期"})[a]||"未知",V=a=>a.suppliers?a.suppliers.length:0,K=a=>a.suppliers?a.suppliers.filter(t=>t.status==="quoted").length:0,R=a=>a.suppliers?a.suppliers.filter(t=>t.status==="rejected").length:0,G=()=>p.value.materials.reduce((a,t)=>a+t.quantity,0),U=()=>{const a=p.value.materials.reduce((t,u)=>{const r=u.suppliers.find(q=>q.status==="accepted");return r?t+r.totalPrice:t},0);return a>0?`¥${a.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})}`:"-"},A=()=>{T.info("跳转到注册页面")},F=()=>{b.value=!0,T.success("登录成功！")},H=()=>{T.info("跳转到平台采纳报价页面")},J=async()=>{try{console.log("获取询价单详情"),await new Promise(a=>setTimeout(a,500)),p.value={id:"1",rfqNo:"RFQ-2023-0001",status:"inProgress",createTime:"2023-10-15 09:30:00",inquiryTime:"2023-10-15 10:00:00",deadline:"2023-10-25 18:00:00",endTime:"2023-10-26 18:00:00",creator:"张三",contactPhone:"138****8888",materialModelCount:4,remark:"紧急采购项目，欢迎供应商报价",materials:Array.from({length:4}).map((a,t)=>{const u=Math.floor(Math.random()*100+10),r=Array.from({length:3+Math.floor(Math.random()*2)}).map((f,g)=>{const I=["pending","quoted","rejected","expired"],y=g===0?"quoted":I[g%I.length],v=Math.floor(Math.random()*1e3+100);return{id:`supplier-${t}-${g}`,name:`供应商${String.fromCharCode(65+g)}`,price:y==="quoted"?v:0,totalPrice:y==="quoted"?v*u:0,minOrderQuantity:y==="quoted"?Math.floor(Math.random()*100+10):0,promisedDelivery:y==="quoted"?`${Math.floor(Math.random()*30+15)}天`:"",validityPeriod:y==="quoted"?"2023-11-05":"",quoteTime:y==="quoted"?`2023-10-${16+g} 10:30:00`:"",status:y,remark:y==="quoted"&&g%2===0?"含税价格":""}}),q=r.filter(f=>f.status==="quoted"&&f.price>0),C=q.length>0?{min:Math.min(...q.map(f=>f.price)),max:Math.max(...q.map(f=>f.price))}:null;return{id:`material-${t}`,name:`电子元件 ${t+1}`,model:`MODEL-${100+t}`,brand:t%3===0?"品牌A":t%3===1?"品牌B":"品牌C",quantity:u,expectedDelivery:"30天",status:"inProgress",remark:t%2===0?"紧急采购":"",acceptAlternative:t%3===0,suppliers:r,priceRange:C}})}}catch{T.error("获取询价单详情失败")}};return Y(()=>{J()}),(a,t)=>{const u=h("a-button"),r=h("a-col"),q=h("a-row"),C=h("a-card"),f=h("a-alert"),g=h("a-tag"),I=h("a-table"),y=h("a-space");return s(),n("div",Z,[e("div",tt,[e("div",et,[t[3]||(t[3]=e("div",{class:"logo-section"},[e("img",{src:W,alt:"研选工场",class:"logo-image"})],-1)),e("div",at,[b.value?(s(),M(u,{key:1,type:"primary",onClick:H},{default:o(()=>t[2]||(t[2]=[m("前往平台，采纳报价")])),_:1})):(s(),n(k,{key:0},[l(u,{onClick:F},{default:o(()=>t[0]||(t[0]=[m("登录")])),_:1}),l(u,{type:"primary",onClick:A},{default:o(()=>t[1]||(t[1]=[m("立即注册")])),_:1})],64))])])]),e("div",st,[e("div",nt,[l(C,{title:"询价单详情",class:"detail-card"},{default:o(()=>[l(q,{gutter:24},{default:o(()=>[l(r,{span:8},{default:o(()=>[e("div",ot,[t[4]||(t[4]=e("span",{class:"label"},"公司名称：",-1)),e("span",it,i(N.value.fullName),1)])]),_:1}),l(r,{span:8},{default:o(()=>[e("div",lt,[t[5]||(t[5]=e("span",{class:"label"},"询价单号：",-1)),e("span",dt,i(p.value.rfqNo),1)])]),_:1}),l(r,{span:8},{default:o(()=>[e("div",rt,[t[6]||(t[6]=e("span",{class:"label"},"询价时间：",-1)),e("span",ut,i(S(p.value.inquiryTime)),1)])]),_:1}),l(r,{span:8},{default:o(()=>[e("div",ct,[t[7]||(t[7]=e("span",{class:"label"},"截止时间：",-1)),e("span",pt,i(S(p.value.deadline)),1)])]),_:1}),l(r,{span:8},{default:o(()=>[e("div",mt,[t[8]||(t[8]=e("span",{class:"label"},"询价人：",-1)),e("span",_t,i(p.value.creator),1)])]),_:1}),l(r,{span:8},{default:o(()=>[e("div",yt,[t[9]||(t[9]=e("span",{class:"label"},"联系电话：",-1)),e("span",vt,i(p.value.contactPhone),1)])]),_:1}),l(r,{span:8},{default:o(()=>[e("div",ft,[t[10]||(t[10]=e("span",{class:"label"},"物料种类：",-1)),e("span",gt,i(p.value.materialModelCount)+" 种",1)])]),_:1}),l(r,{span:8},{default:o(()=>[e("div",kt,[t[11]||(t[11]=e("span",{class:"label"},"物料总数：",-1)),e("span",ht,i(G())+" 件",1)])]),_:1}),l(r,{span:8},{default:o(()=>[e("div",xt,[t[12]||(t[12]=e("span",{class:"label"},"已采纳总价：",-1)),e("span",qt,i(U()),1)])]),_:1})]),_:1})]),_:1})]),e("div",bt,[l(C,{title:"物料清单"},{default:o(()=>[l(f,{message:b.value?"以下为物料明细及供应商报价情况，点击展开查看各供应商的详细报价。":"以下为物料明细，登录后可查看供应商报价详情。",type:"info","show-icon":"",style:{"margin-bottom":"16px"}},null,8,["message"]),e("div",wt,[l(u,{type:"primary",onClick:E,icon:w.value?"up":"down"},{default:o(()=>[m(i(w.value?"全部收起":"全部展开"),1)]),_:1},8,["icon"])]),l(I,{columns:$,"data-source":p.value.materials,size:"middle",pagination:{pageSize:10},"row-key":"id",bordered:"",scroll:{x:1200},expandedRowKeys:x.value,onExpand:Q},{bodyCell:o(({column:v,record:d})=>[v.dataIndex==="acceptAlternative"?(s(),n(k,{key:0},[m(i(d.acceptAlternative?"是":"否"),1)],64)):_("",!0),v.dataIndex==="status"?(s(),M(g,{key:1,color:B(d.status)},{default:o(()=>[m(i(O(d.status)),1)]),_:2},1032,["color"])):_("",!0),v.dataIndex==="quoteProgress"?(s(),n("div",It,[e("span",null,"供应商数："+i(V(d))+"，",1),e("span",null,[t[13]||(t[13]=m("已报价：")),e("span",Pt,i(K(d)),1),t[14]||(t[14]=m("，"))]),e("span",null,[t[15]||(t[15]=m("已拒绝：")),e("span",Ct,i(R(d)),1)])])):_("",!0),v.dataIndex==="priceRange"?(s(),n(k,{key:3},[b.value&&d.priceRange?(s(),n("span",Mt," ¥"+i(d.priceRange.min.toFixed(2))+" - ¥"+i(d.priceRange.max.toFixed(2)),1)):b.value?(s(),n("span",St,"-")):(s(),n("span",Tt,"需登录查看"))],64)):_("",!0)]),expandedRowRender:o(({record:v})=>[e("div",Dt,[t[21]||(t[21]=e("h4",{style:{"margin-bottom":"12px",color:"#262626"}},"供应商报价详情",-1)),b.value?(s(),M(I,{key:1,size:"small",columns:D,"data-source":v.suppliers,pagination:!1,"row-key":"id",bordered:""},{bodyCell:o(({column:d,record:c})=>[d.dataIndex==="price"?(s(),n(k,{key:0},[c.status==="quoted"?(s(),n("span",Qt," ¥"+i(c.price.toFixed(2)),1)):(s(),n("span",jt,"-"))],64)):_("",!0),d.dataIndex==="totalPrice"?(s(),n(k,{key:1},[c.status==="quoted"?(s(),n("span",Et," ¥"+i(c.totalPrice.toFixed(2)),1)):(s(),n("span",Ot,"-"))],64)):_("",!0),d.dataIndex==="minOrderQuantity"?(s(),n(k,{key:2},[c.status==="quoted"?(s(),n("span",Bt,i(c.minOrderQuantity),1)):(s(),n("span",zt,"-"))],64)):_("",!0),d.dataIndex==="promisedDelivery"?(s(),n(k,{key:3},[c.status==="quoted"?(s(),n("span",Lt,i(c.promisedDelivery),1)):(s(),n("span",Vt,"-"))],64)):_("",!0),d.dataIndex==="validityPeriod"?(s(),n(k,{key:4},[c.status==="quoted"?(s(),n("span",Kt,i(c.validityPeriod),1)):(s(),n("span",Rt,"-"))],64)):_("",!0),d.dataIndex==="quoteTime"?(s(),n(k,{key:5},[c.status==="quoted"?(s(),n("span",Gt,i(S(c.quoteTime)),1)):(s(),n("span",Ut,"-"))],64)):_("",!0),d.dataIndex==="status"?(s(),M(g,{key:6,color:z(c.status)},{default:o(()=>[m(i(L(c.status)),1)]),_:2},1032,["color"])):_("",!0)]),_:2},1032,["data-source"])):(s(),n("div",At,[e("div",Ft,[l(I,{size:"small",columns:D,"data-source":v.suppliers,pagination:!1,"row-key":"id",bordered:"",class:"blurred-table"},{bodyCell:o(()=>t[16]||(t[16]=[e("span",{class:"blurred-text"},"***",-1)])),_:2},1032,["data-source"])]),e("div",Nt,[e("div",$t,[t[19]||(t[19]=e("h4",null,"查看供应商报价需要登录",-1)),t[20]||(t[20]=e("p",null,"登录后可以查看详细的供应商报价信息",-1)),l(y,null,{default:o(()=>[l(u,{onClick:F},{default:o(()=>t[17]||(t[17]=[m("登录")])),_:1}),l(u,{type:"primary",onClick:A},{default:o(()=>t[18]||(t[18]=[m("立即注册")])),_:1})]),_:1})])])]))])]),_:1},8,["data-source","expandedRowKeys"])]),_:1})])])])}}},Zt=X(Ht,[["__scopeId","data-v-fc78fcb1"]]);export{Zt as default};
