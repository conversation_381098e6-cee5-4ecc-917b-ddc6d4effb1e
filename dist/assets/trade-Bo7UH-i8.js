import{_ as h}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{r as l,c as f,b as w,d as s,g as e,w as t,h as n,t as c,o as C}from"./index-B4CbS3Hl.js";const k={class:"trade-info-container"},B={class:"wrapper"},D={class:"info-item"},L={class:"value"},M={class:"info-item"},N={class:"value"},P={class:"info-item"},V={class:"value"},E={class:"info-item"},I={class:"value"},S={class:"credit-progress"},T={__name:"trade",setup(U){l([{type:"cash",name:"现金/电汇",enabled:!0},{type:"acceptance",name:"银行承兑",enabled:!1}]);const v=l("现金/电汇"),m=l("账期结算"),b=l(30),g=l(15),r=l(5e6),d=l(32e5),y=f(()=>r.value-d.value),p=f(()=>Math.round(d.value/r.value*100));return l(156),l(28e5),l(85e4),l(12e4),(j,a)=>{const o=n("a-col"),i=n("a-row"),u=n("a-card"),_=n("a-statistic"),x=n("a-progress");return C(),w("div",k,[s("div",B,[e(u,{title:"付款方式",class:"info-card",bordered:!1},{default:t(()=>[e(i,{gutter:24},{default:t(()=>[e(o,{span:12},{default:t(()=>[s("div",D,[a[0]||(a[0]=s("span",{class:"label"},"付款方式：",-1)),s("span",L,c(v.value),1)])]),_:1})]),_:1})]),_:1}),e(u,{title:"付款条件",class:"info-card",bordered:!1},{default:t(()=>[e(i,{gutter:24},{default:t(()=>[e(o,{span:8},{default:t(()=>[s("div",M,[a[1]||(a[1]=s("span",{class:"label"},"付款条件：",-1)),s("span",N,c(m.value),1)])]),_:1}),e(o,{span:8},{default:t(()=>[s("div",P,[a[2]||(a[2]=s("span",{class:"label"},"账期天数：",-1)),s("span",V,c(b.value)+"天",1)])]),_:1}),e(o,{span:8},{default:t(()=>[s("div",E,[a[3]||(a[3]=s("span",{class:"label"},"账期日：",-1)),s("span",I,"每月"+c(g.value)+"号",1)])]),_:1})]),_:1})]),_:1}),e(u,{title:"账期额度管理",class:"info-card",bordered:!1},{default:t(()=>[e(i,{gutter:24},{default:t(()=>[e(o,{span:8},{default:t(()=>[e(_,{title:"总账期额度",value:r.value,suffix:"元","value-style":{color:"#1890ff"}},null,8,["value"])]),_:1}),e(o,{span:8},{default:t(()=>[e(_,{title:"已使用额度",value:d.value,suffix:"元","value-style":{color:"#f5222d"}},null,8,["value"])]),_:1}),e(o,{span:8},{default:t(()=>[e(_,{title:"剩余额度",value:y.value,suffix:"元","value-style":{color:"#52c41a"}},null,8,["value"])]),_:1})]),_:1}),s("div",S,[a[4]||(a[4]=s("span",{class:"progress-label"},"额度使用情况：",-1)),e(x,{percent:p.value,"stroke-color":p.value>80?"#f5222d":"#1890ff","show-info":!0},null,8,["percent","stroke-color"])])]),_:1}),a[5]||(a[5]=s("div",{class:"action-buttons"},null,-1))])])}}},A=h(T,[["__scopeId","data-v-63c6191c"]]);export{A as default};
