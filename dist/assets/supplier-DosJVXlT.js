import{r as _,a as U,c as X,m as Ee,b as v,d as a,g as o,w as l,h as u,j as i,t as d,f as w,k as m,F as V,i as Be,s as Z,n as N,u as Re,o as n,l as Oe,y as Ve}from"./index-B4CbS3Hl.js";import{_ as Ae}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{M as Ke}from"./MoreOutlined-xtgiaAC5.js";const je={class:"fav-supplier-container"},qe={class:"search-filter-section"},Je={class:"search-card"},Qe={class:"filters-panel"},Ye={class:"tab-section"},Ge={class:"quick-actions-bar"},He={class:"actions-left"},We={class:"actions-right"},Xe={class:"main-content"},Ze={key:0,class:"card-view"},et={key:0,class:"empty-state"},tt={key:1,class:"supplier-cards-container"},at={class:"card-content"},lt={class:"supplier-header"},st={class:"supplier-logo"},ot=["src","alt"],it={key:1,class:"default-logo"},nt={class:"supplier-name-section"},rt={class:"supplier-name"},dt={class:"supplier-tags"},ut={class:"header-actions"},ct={class:"status-indicators"},ft={class:"supplier-location"},vt={class:"supplier-details"},mt={class:"detail-row"},pt={class:"detail-value"},yt={class:"detail-row"},_t={class:"detail-value"},gt={class:"detail-row"},kt={class:"detail-value"},ht={class:"supplier-details"},bt={class:"detail-row"},wt={class:"time-value"},Ct={key:0,class:"detail-row"},Tt={class:"favoriter-info"},xt={class:"detail-row"},Ft={class:"note-content"},Nt={class:"card-actions"},St={key:2,class:"card-pagination"},Mt={key:1,class:"list-view"},zt={key:0,class:"supplier-name-cell"},Pt=["onClick"],$t={class:"name-tags"},Ut={key:0,class:"favoriter-cell"},Dt={key:1,class:"self-favorited"},Lt={key:3,class:"note-cell"},It={key:0,class:"note-text"},Et={key:1,class:"no-note"},Bt={key:0,class:"supplier-detail-content"},Rt={__name:"supplier",setup(Ot){Re();const D=_(!1),L=_("card"),S=_("addTime"),b=U({keyword:"",supplierType:""}),M=_(!1),I=_(!1),A=_(!1),K=_(!1),g=_("my"),k=_([]),y=_(null),j=_(null),E=U({note:""}),q=U({note:""}),f=U({current:1,pageSize:12,total:0}),ue=X(()=>({current:f.current,pageSize:f.pageSize,total:f.total,showSizeChanger:!0,showQuickJumper:!0,showTotal:(t,e)=>`第 ${e[0]}-${e[1]} 条，共 ${t} 条`})),z=U({total:34,recommended:12,thisMonth:5}),B=_([]),J=_(0),ee=_(0),te=_(0),Q=[{id:"1",companyName:"深圳精密机械有限公司",region:"广东省深圳市",logoUrl:"https://via.placeholder.com/80x80/1890ff/ffffff?text=深圳精密",factoryLevel:"A级",levelScore:95,industry:"机械制造",establishmentTime:14200704e5,registeredCapital:"500万人民币",supplierType:"recommended",favoriteTime:16409952e5,note:"优质供应商，产品质量稳定，交期准时",isFavorited:!0,isMyFavorite:!0,isPublic:!1,favoriterName:"张三"},{id:"2",companyName:"苏州电子科技股份有限公司",region:"江苏省苏州市",logoUrl:"https://via.placeholder.com/80x80/52c41a/ffffff?text=苏州电子",factoryLevel:"B级",levelScore:88,industry:"电子电气",establishmentTime:1325376e6,registeredCapital:"800万人民币",supplierType:"normal",favoriteTime:16410816e5,note:"",isFavorited:!0,isMyFavorite:!0,isPublic:!1,favoriterName:"李四"},{id:"3",companyName:"上海智能制造有限公司",region:"上海市浦东新区",logoUrl:"",factoryLevel:"A级",levelScore:92,industry:"自动化设备",establishmentTime:14832e8,registeredCapital:"1200万人民币",supplierType:"recommended",favoriteTime:1641168e6,note:"技术实力强，适合长期合作",isFavorited:!0,isMyFavorite:!0,isPublic:!1,favoriterName:"王五"},{id:"4",companyName:"北京新材料科技有限公司",region:"北京市海淀区",logoUrl:"https://via.placeholder.com/80x80/ffc107/ffffff?text=北京新材料",factoryLevel:"A级",levelScore:90,industry:"新材料",establishmentTime:15e11,registeredCapital:"1000万人民币",supplierType:"normal",favoriteTime:16412544e5,note:"服务态度好，价格合理",isFavorited:!0,isMyFavorite:!1,isPublic:!0,favoriterName:"赵六"},{id:"5",companyName:"广州化工有限公司",region:"广东省广州市",logoUrl:"https://via.placeholder.com/80x80/007bff/ffffff?text=广州化工",factoryLevel:"B级",levelScore:85,industry:"化工",establishmentTime:145e10,registeredCapital:"500万人民币",supplierType:"recommended",favoriteTime:16413408e5,note:"产品质量稳定，交期准时",isFavorited:!0,isMyFavorite:!1,isPublic:!1,favoriterName:"钱七"}],ce=X(()=>({selectedRowKeys:k.value,onChange:t=>{k.value=t}}));_([{title:"供应商名称",dataIndex:"companyName",key:"name",width:200,fixed:"left"},{title:"所在地区",dataIndex:"region",key:"location",width:140},{title:"行业类别",dataIndex:"industry",key:"industry",width:120},{title:"收藏时间",key:"favoriteTime",width:120,sorter:!0},{title:"备注",key:"note",width:200},{title:"操作",key:"actions",width:200,fixed:"right"}]);const fe=()=>g.value==="my"?[{title:"供应商名称",dataIndex:"companyName",key:"name",width:200,fixed:"left"},{title:"所在地区",dataIndex:"region",key:"location",width:140},{title:"行业类别",dataIndex:"industry",key:"industry",width:120},{title:"收藏时间",key:"favoriteTime",width:120,sorter:!0},{title:"备注",key:"note",width:200},{title:"收藏人",key:"favoriter",width:100},{title:"操作",key:"actions",width:200,fixed:"right"}]:[{title:"供应商名称",dataIndex:"companyName",key:"name",width:200,fixed:"left"},{title:"所在地区",dataIndex:"region",key:"location",width:140},{title:"行业类别",dataIndex:"industry",key:"industry",width:120},{title:"收藏时间",key:"favoriteTime",width:120,sorter:!0},{title:"备注",key:"note",width:200},{title:"收藏人",key:"favoriter",width:100},{title:"操作",key:"actions",width:200,fixed:"right"}],ve=()=>g.value==="my"?ce.value:null,R=X(()=>{let t=[...Q];if(g.value==="my"?t=t.filter(r=>r.isMyFavorite):g.value==="public"&&(t=t.filter(r=>r.isPublic)),b.keyword){const r=b.keyword.toLowerCase();t=t.filter(h=>h.companyName.toLowerCase().includes(r))}b.supplierType&&(t=t.filter(r=>r.supplierType===b.supplierType)),S.value==="addTime"?t.sort((r,h)=>h.favoriteTime-r.favoriteTime):S.value==="companyName"&&t.sort((r,h)=>r.companyName.localeCompare(h.companyName)),f.total=t.length;const e=(f.current-1)*f.pageSize,p=e+f.pageSize;return t.slice(e,p)}),me=()=>g.value==="my"?"暂无我的收藏供应商":g.value==="public"?"暂无公开的收藏供应商":"暂无收藏的供应商",F=async()=>{D.value=!0;try{await new Promise(r=>setTimeout(r,500));let t=[...Q];if(g.value==="my"?t=t.filter(r=>r.isMyFavorite):g.value==="public"&&(t=t.filter(r=>r.isPublic)),b.keyword){const r=b.keyword.toLowerCase();t=t.filter(h=>h.companyName.toLowerCase().includes(r))}b.supplierType&&(t=t.filter(r=>r.supplierType===b.supplierType)),S.value==="addTime"?t.sort((r,h)=>h.favoriteTime-r.favoriteTime):S.value==="companyName"&&t.sort((r,h)=>r.companyName.localeCompare(h.companyName)),f.total=t.length;const e=(f.current-1)*f.pageSize,p=e+f.pageSize;B.value=t.slice(e,p)}catch{N.error("搜索失败，请重试")}finally{D.value=!1}},pe=()=>{b.keyword="",b.supplierType="",f.current=1,F()},ye=t=>{g.value=t,k.value=[],f.current=1,F()},_e=(t,e)=>{f.current=t,f.pageSize=e,F()},ge=(t,e,p)=>{f.current=t.current,f.pageSize=t.pageSize,p.field&&p.field==="favoriteTime"&&(S.value="addTime"),F()},ke=t=>{const e=k.value.indexOf(t);e>-1?k.value.splice(e,1):k.value.push(t)},ae=t=>{y.value=t,M.value=!0},he=t=>{t.target.style.display="none";const e=t.target.nextElementSibling;e&&(e.style.display="flex")},Y=t=>{j.value=t,E.note=t.note||"",I.value=!0},be=()=>{j.value&&(j.value.note=E.note,N.success("备注保存成功"),I.value=!1)},le=t=>{t.isFavorited=!1,t.isMyFavorite=!1,t.isPublic=!1,t.favoriterName=null,N.success(`已将 ${t.companyName} 从收藏夹移除`),z.total--,J.value--,t.supplierType==="recommended"&&z.recommended--,M.value&&(M.value=!1)},we=()=>{const t=B.value.filter(e=>k.value.includes(e.id));t.forEach(e=>{e.note=q.note}),N.success(`已批量添加 ${t.length} 家供应商的备注`),k.value=[],A.value=!1},Ce=()=>{const t=B.value.filter(e=>k.value.includes(e.id));t.forEach(e=>{e.isPublic=!0}),N.success(`已批量将 ${t.length} 家供应商设为公开`),k.value=[],K.value=!1},se=t=>{t.isPublic=!t.isPublic,N.success(`已将 ${t.companyName} 设为${t.isPublic?"公开":"私密"}`)},G=t=>t?new Date(t).toLocaleDateString("zh-CN"):"",oe=t=>t?new Date(t).getFullYear():"";return Ee(()=>{const t=Q.filter(p=>p.isFavorited);B.value=t.slice(0,f.pageSize),f.total=t.length,z.total=t.length,z.recommended=t.filter(p=>p.supplierType==="recommended").length;const e=new Date;e.setMonth(e.getMonth()-1),z.thisMonth=t.filter(p=>p.favoriteTime>e.getTime()).length,J.value=t.filter(p=>p.isMyFavorite).length,ee.value=t.filter(p=>p.isPublic).length,te.value=t.length}),(t,e)=>{const p=u("a-input"),r=u("a-form-item"),h=u("a-col"),T=u("a-button"),P=u("a-space"),Te=u("a-row"),H=u("a-tab-pane"),xe=u("a-tabs"),ie=u("a-radio-button"),Fe=u("a-radio-group"),Ne=u("a-empty"),Se=u("a-checkbox"),$=u("a-tag"),W=u("a-menu-item"),Me=u("a-menu"),ze=u("a-dropdown"),ne=u("a-tooltip"),Pe=u("a-divider"),$e=u("a-card"),Ue=u("a-pagination"),De=u("a-avatar"),Le=u("a-table"),C=u("a-descriptions-item"),Ie=u("a-descriptions"),O=u("a-modal"),re=u("a-textarea"),de=u("a-form");return n(),v("div",je,[a("div",qe,[a("div",Je,[a("div",Qe,[o(Te,{gutter:16},{default:l(()=>[o(h,{span:8},{default:l(()=>[o(r,{label:"供应商名称"},{default:l(()=>[o(p,{value:b.keyword,"onUpdate:value":e[0]||(e[0]=s=>b.keyword=s),placeholder:"搜索收藏的供应商","allow-clear":"",onPressEnter:F},null,8,["value"])]),_:1})]),_:1}),o(h,{span:6},{default:l(()=>[o(r,{style:{"margin-bottom":"0"}},{default:l(()=>[o(P,null,{default:l(()=>[o(T,{type:"primary",onClick:F,loading:D.value},{default:l(()=>e[12]||(e[12]=[a("i",{class:"fas fa-search"},null,-1),i(" 搜索 ")])),_:1},8,["loading"]),o(T,{onClick:pe},{default:l(()=>e[13]||(e[13]=[a("i",{class:"fas fa-undo"},null,-1),i(" 重置 ")])),_:1})]),_:1})]),_:1})]),_:1})]),_:1})])])]),a("div",Ye,[o(xe,{activeKey:g.value,"onUpdate:activeKey":e[1]||(e[1]=s=>g.value=s),onChange:ye,class:"favorites-tabs"},{default:l(()=>[o(H,{key:"my",tab:"我的收藏"},{tab:l(()=>[a("span",null,[e[14]||(e[14]=a("i",{class:"fas fa-heart"},null,-1)),i(" 我的收藏 ("+d(J.value)+") ",1)])]),_:1}),o(H,{key:"public",tab:"公开的收藏"},{tab:l(()=>[a("span",null,[e[15]||(e[15]=a("i",{class:"fas fa-share-alt"},null,-1)),i(" 公开的收藏 ("+d(ee.value)+") ",1)])]),_:1}),o(H,{key:"all",tab:"全部"},{tab:l(()=>[a("span",null,[e[16]||(e[16]=a("i",{class:"fas fa-list"},null,-1)),i(" 全部 ("+d(te.value)+") ",1)])]),_:1})]),_:1},8,["activeKey"])]),a("div",Ge,[a("div",He,[g.value==="my"&&k.value.length>0?(n(),w(P,{key:0})):m("",!0)]),a("div",We,[o(P,null,{default:l(()=>[o(Fe,{value:L.value,"onUpdate:value":e[2]||(e[2]=s=>L.value=s),"button-style":"solid",size:"small"},{default:l(()=>[o(ie,{value:"card"},{default:l(()=>e[17]||(e[17]=[a("i",{class:"fas fa-th-large"},null,-1),i(" 卡片 ")])),_:1}),o(ie,{value:"list"},{default:l(()=>e[18]||(e[18]=[a("i",{class:"fas fa-list"},null,-1),i(" 列表 ")])),_:1})]),_:1},8,["value"])]),_:1})])]),a("div",Xe,[L.value==="card"?(n(),v("div",Ze,[R.value.length===0?(n(),v("div",et,[o(Ne,{description:me()},{image:l(()=>e[19]||(e[19]=[a("i",{class:"fas fa-heart-broken",style:{"font-size":"48px",color:"#d9d9d9"}},null,-1)])),default:l(()=>[o(T,{type:"primary",onClick:e[3]||(e[3]=s=>t.$router.push("/workspace/supplier/index"))},{default:l(()=>e[20]||(e[20]=[a("i",{class:"fas fa-search"},null,-1),i(" 去发现供应商 ")])),_:1})]),_:1},8,["description"])])):(n(),v("div",tt,[(n(!0),v(V,null,Be(R.value,s=>(n(),v("div",{key:s.id,class:"supplier-card-wrapper"},[o($e,{class:Z(["supplier-card",{selected:k.value.includes(s.id)}])},{default:l(()=>[a("div",at,[a("div",lt,[g.value==="my"||g.value==="all"&&s.isMyFavorite?(n(),w(Se,{key:0,checked:k.value.includes(s.id),onChange:()=>ke(s.id),class:"card-checkbox"},null,8,["checked","onChange"])):m("",!0),a("div",st,[s.logoUrl?(n(),v("img",{key:0,src:s.logoUrl,alt:s.companyName,onError:he},null,40,ot)):(n(),v("div",it,e[21]||(e[21]=[a("i",{class:"fas fa-building"},null,-1)])))]),a("div",nt,[a("div",rt,d(s.companyName),1),a("div",dt,[o($,{color:"blue",size:"small"},{default:l(()=>[i(d(s.factoryLevel),1)]),_:2},1024),s.isPublic&&s.isMyFavorite?(n(),w($,{key:0,color:"green",size:"small"},{default:l(()=>e[22]||(e[22]=[a("i",{class:"fas fa-share-alt"},null,-1),i(" 已公开 ")])),_:1})):m("",!0)])]),a("div",ut,[s.isMyFavorite?(n(),w(ze,{key:0,trigger:["click"],placement:"bottomRight"},{overlay:l(()=>[o(Me,null,{default:l(()=>[o(W,{key:"edit-note",onClick:c=>Y(s)},{default:l(()=>e[23]||(e[23]=[a("i",{class:"fas fa-edit"},null,-1),i(" 编辑备注 ")])),_:2},1032,["onClick"]),o(W,{key:"toggle-public",onClick:c=>se(s)},{default:l(()=>[a("i",{class:Z(s.isPublic?"fas fa-eye-slash":"fas fa-share-alt")},null,2),i(" "+d(s.isPublic?"取消公开":"设为公开"),1)]),_:2},1032,["onClick"]),o(W,{key:"remove",onClick:c=>le(s),class:"danger-item"},{default:l(()=>e[24]||(e[24]=[a("i",{class:"fas fa-heart-broken"},null,-1),i(" 取消收藏 ")])),_:2},1032,["onClick"])]),_:2},1024)]),default:l(()=>[o(T,{type:"text",size:"small",class:"action-menu-btn"},{default:l(()=>[o(Oe(Ke))]),_:1})]),_:2},1024)):m("",!0),a("div",ct,[s.isPublic&&s.isMyFavorite?(n(),w(ne,{key:0,title:"已公开"},{default:l(()=>e[25]||(e[25]=[a("i",{class:"fas fa-share-alt status-icon public"},null,-1)])),_:1})):m("",!0),s.isMyFavorite?(n(),w(ne,{key:1,title:"我的收藏"},{default:l(()=>e[26]||(e[26]=[a("i",{class:"fas fa-heart status-icon favorite"},null,-1)])),_:1})):m("",!0)])])]),a("div",ft,[e[27]||(e[27]=a("i",{class:"fas fa-map-marker-alt"},null,-1)),a("span",null,d(s.region),1)]),a("div",vt,[a("div",mt,[e[28]||(e[28]=a("span",{class:"detail-label"},"行业类别",-1)),a("span",pt,d(s.industry),1)]),a("div",yt,[e[29]||(e[29]=a("span",{class:"detail-label"},"成立时间",-1)),a("span",_t,d(oe(s.establishmentTime))+"年",1)]),a("div",gt,[e[30]||(e[30]=a("span",{class:"detail-label"},"注册资本",-1)),a("span",kt,d(s.registeredCapital),1)])]),o(Pe),a("div",ht,[a("div",bt,[e[31]||(e[31]=a("span",{class:"detail-label"},"收藏时间",-1)),a("div",wt,d(G(s.favoriteTime)),1)]),s.isMyFavorite?m("",!0):(n(),v("div",Ct,[e[32]||(e[32]=a("span",{class:"detail-label"},"收藏人",-1)),a("div",Tt,d(s.favoriterName),1)])),a("div",xt,[e[33]||(e[33]=a("span",{class:"detail-label"},"备注",-1)),a("div",Ft,d(s.note||"暂无备注"),1)])]),a("div",Nt,[o(T,{type:"primary",onClick:Ve(c=>ae(s),["stop"]),block:""},{default:l(()=>e[34]||(e[34]=[a("i",{class:"fas fa-eye"},null,-1),i(" 查看详情 ")])),_:2},1032,["onClick"])])])]),_:2},1032,["class"])]))),128))])),R.value.length>0?(n(),v("div",St,[o(Ue,{current:f.current,"onUpdate:current":e[4]||(e[4]=s=>f.current=s),"page-size":f.pageSize,"onUpdate:pageSize":e[5]||(e[5]=s=>f.pageSize=s),total:f.total,"show-size-changer":!0,"show-quick-jumper":!0,"show-total":(s,c)=>`第 ${c[0]}-${c[1]} 条，共 ${s} 条`,onChange:_e},null,8,["current","page-size","total","show-total"])])):m("",!0)])):L.value==="list"?(n(),v("div",Mt,[o(Le,{columns:fe(),"data-source":R.value,pagination:ue.value,loading:D.value,"row-selection":ve(),"row-key":"id",onChange:ge,class:"supplier-table",size:"small",bordered:""},{bodyCell:l(({column:s,record:c})=>[s.key==="name"?(n(),v("div",zt,[a("a",{onClick:x=>ae(c),style:{color:"#f94c30"}},d(c.companyName),9,Pt),a("div",$t,[c.isPublic&&c.isMyFavorite?(n(),w($,{key:0,color:"green",size:"small"},{default:l(()=>e[35]||(e[35]=[a("i",{class:"fas fa-share-alt"},null,-1),i(" 已公开 ")])),_:1})):m("",!0)])])):m("",!0),s.key==="favoriter"?(n(),v(V,{key:1},[c.isMyFavorite?(n(),v("span",Dt,"我的收藏")):(n(),v("div",Ut,[o(De,{size:24,style:{"margin-right":"8px"}},{default:l(()=>{var x;return[i(d((x=c.favoriterName)==null?void 0:x[0]),1)]}),_:2},1024),i(" "+d(c.favoriterName),1)]))],64)):m("",!0),s.key==="favoriteTime"?(n(),v(V,{key:2},[i(d(G(c.favoriteTime)),1)],64)):m("",!0),s.key==="note"?(n(),v("div",Lt,[c.note?(n(),v("span",It,d(c.note),1)):(n(),v("span",Et,"暂无备注")),c.isMyFavorite?(n(),w(T,{key:2,type:"link",size:"small",onClick:x=>Y(c)},{default:l(()=>e[36]||(e[36]=[a("i",{class:"fas fa-edit"},null,-1)])),_:2},1032,["onClick"])):m("",!0)])):m("",!0),s.key==="actions"?(n(),w(P,{key:4},{default:l(()=>[c.isMyFavorite?(n(),v(V,{key:0},[o(T,{type:"link",size:"small",onClick:x=>Y(c)},{default:l(()=>e[37]||(e[37]=[a("i",{class:"fas fa-edit"},null,-1),i(" 编辑备注 ")])),_:2},1032,["onClick"]),o(T,{type:"link",size:"small",onClick:x=>se(c)},{default:l(()=>[a("i",{class:Z(c.isPublic?"fas fa-eye-slash":"fas fa-share-alt")},null,2),i(" "+d(c.isPublic?"取消公开":"设为公开"),1)]),_:2},1032,["onClick"]),o(T,{type:"link",size:"small",danger:"",onClick:x=>le(c)},{default:l(()=>e[38]||(e[38]=[a("i",{class:"fas fa-heart-broken"},null,-1),i(" 取消收藏 ")])),_:2},1032,["onClick"])],64)):m("",!0)]),_:2},1024)):m("",!0)]),_:1},8,["columns","data-source","pagination","loading","row-selection"])])):m("",!0)]),o(O,{open:M.value,"onUpdate:open":e[6]||(e[6]=s=>M.value=s),title:"供应商详情",width:"800px",footer:null,class:"supplier-detail-modal"},{default:l(()=>[y.value?(n(),v("div",Bt,[o(Ie,{column:2,bordered:""},{default:l(()=>[o(C,{label:"企业名称"},{default:l(()=>[i(d(y.value.companyName),1)]),_:1}),o(C,{label:"所在地区"},{default:l(()=>[i(d(y.value.region),1)]),_:1}),o(C,{label:"行业类别"},{default:l(()=>[i(d(y.value.industry),1)]),_:1}),o(C,{label:"工厂等级"},{default:l(()=>[i(d(y.value.factoryLevel),1)]),_:1}),o(C,{label:"等级分数"},{default:l(()=>[i(d(y.value.levelScore)+"分",1)]),_:1}),o(C,{label:"成立时间"},{default:l(()=>[i(d(oe(y.value.establishmentTime))+"年",1)]),_:1}),o(C,{label:"注册资本"},{default:l(()=>[i(d(y.value.registeredCapital),1)]),_:1}),o(C,{label:"收藏时间"},{default:l(()=>[i(d(G(y.value.favoriteTime)),1)]),_:1}),y.value.isMyFavorite?m("",!0):(n(),w(C,{key:0,label:"收藏人"},{default:l(()=>[i(d(y.value.favoriterName),1)]),_:1})),o(C,{label:"收藏状态"},{default:l(()=>[o(P,null,{default:l(()=>[y.value.isMyFavorite?(n(),w($,{key:0,color:"red"},{default:l(()=>e[39]||(e[39]=[a("i",{class:"fas fa-heart"},null,-1),i(" 我的收藏 ")])),_:1})):m("",!0),y.value.isPublic&&y.value.isMyFavorite?(n(),w($,{key:1,color:"green"},{default:l(()=>e[40]||(e[40]=[a("i",{class:"fas fa-share-alt"},null,-1),i(" 已公开 ")])),_:1})):m("",!0)]),_:1})]),_:1}),o(C,{label:"备注信息",span:2},{default:l(()=>[i(d(y.value.note||"暂无备注"),1)]),_:1})]),_:1})])):m("",!0)]),_:1},8,["open"]),o(O,{open:I.value,"onUpdate:open":e[8]||(e[8]=s=>I.value=s),title:"编辑备注",width:"500px",onOk:be},{default:l(()=>[o(de,{layout:"vertical"},{default:l(()=>[o(r,{label:"备注信息"},{default:l(()=>[o(re,{value:E.note,"onUpdate:value":e[7]||(e[7]=s=>E.note=s),placeholder:"请输入备注信息...",rows:4,"show-count":"",maxlength:200},null,8,["value"])]),_:1})]),_:1})]),_:1},8,["open"]),o(O,{open:A.value,"onUpdate:open":e[10]||(e[10]=s=>A.value=s),title:"批量编辑备注",width:"500px",onOk:we},{default:l(()=>[o(de,{layout:"vertical"},{default:l(()=>[o(r,{label:"备注信息"},{default:l(()=>[o(re,{value:q.note,"onUpdate:value":e[9]||(e[9]=s=>q.note=s),placeholder:"请输入备注信息...",rows:3},null,8,["value"])]),_:1})]),_:1})]),_:1},8,["open"]),o(O,{open:K.value,"onUpdate:open":e[11]||(e[11]=s=>K.value=s),title:"批量设为公开",width:"500px",onOk:Ce},{default:l(()=>[a("p",null,"确定要将选中的 "+d(k.value.length)+" 个供应商设为公开吗？公开后，同企业的其他成员将能够看到这些收藏。",1)]),_:1},8,["open"])])}}},jt=Ae(Rt,[["__scopeId","data-v-b928fbd1"]]);export{jt as default};
