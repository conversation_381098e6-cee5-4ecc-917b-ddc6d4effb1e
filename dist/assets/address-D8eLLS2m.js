import{_ as se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{g as a,I as S,r as y,a as re,m as ie,b as C,d,w as r,j as b,l as g,P as T,h as p,f as x,F as E,i as ue,k as P,o as m,s as de,t as O,n as k,M as ce}from"./index-B4CbS3Hl.js";import{P as M}from"./PhoneOutlined-DQjXuUfl.js";var ve={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 289.1a362.49 362.49 0 00-79.9-115.7 370.83 370.83 0 00-118.2-77.8C610.7 76.6 562.1 67 512 67c-50.1 0-98.7 9.6-144.5 28.5-44.3 18.3-84 44.5-118.2 77.8A363.6 363.6 0 00169.4 289c-19.5 45-29.4 92.8-29.4 142 0 70.6 16.9 140.9 50.1 208.7 26.7 54.5 64 107.6 111 158.1 80.3 86.2 164.5 138.9 188.4 153a43.9 43.9 0 0022.4 6.1c7.8 0 15.5-2 22.4-6.1 23.9-14.1 108.1-66.8 188.4-153 47-50.4 84.3-103.6 111-158.1C867.1 572 884 501.8 884 431.1c0-49.2-9.9-97-29.4-142zM512 880.2c-65.9-41.9-300-207.8-300-449.1 0-77.9 31.1-151.1 87.6-206.3C356.3 169.5 431.7 139 512 139s155.7 30.5 212.4 85.9C780.9 280 812 353.2 812 431.1c0 241.3-234.1 407.2-300 449.1zm0-617.2c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 551c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 439c0-29.9 11.7-58 32.8-79.2C454 338.6 482.1 327 512 327c29.9 0 58 11.6 79.2 32.8C612.4 381 624 409.1 624 439c0 29.9-11.6 58-32.8 79.2z"}}]},name:"environment",theme:"outlined"};function $(c){for(var n=1;n<arguments.length;n++){var s=arguments[n]!=null?Object(arguments[n]):{},v=Object.keys(s);typeof Object.getOwnPropertySymbols=="function"&&(v=v.concat(Object.getOwnPropertySymbols(s).filter(function(o){return Object.getOwnPropertyDescriptor(s,o).enumerable}))),v.forEach(function(o){pe(c,o,s[o])})}return c}function pe(c,n,s){return n in c?Object.defineProperty(c,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):c[n]=s,c}var D=function(n,s){var v=$({},n,s.attrs);return a(S,$({},v,{icon:ve}),null)};D.displayName="EnvironmentOutlined";D.inheritAttrs=!1;var fe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M744 62H280c-35.3 0-64 28.7-64 64v768c0 35.3 28.7 64 64 64h464c35.3 0 64-28.7 64-64V126c0-35.3-28.7-64-64-64zm-8 824H288V134h448v752zM472 784a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"mobile",theme:"outlined"};function R(c){for(var n=1;n<arguments.length;n++){var s=arguments[n]!=null?Object(arguments[n]):{},v=Object.keys(s);typeof Object.getOwnPropertySymbols=="function"&&(v=v.concat(Object.getOwnPropertySymbols(s).filter(function(o){return Object.getOwnPropertyDescriptor(s,o).enumerable}))),v.forEach(function(o){me(c,o,s[o])})}return c}function me(c,n,s){return n in c?Object.defineProperty(c,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):c[n]=s,c}var w=function(n,s){var v=R({},n,s.attrs);return a(S,R({},v,{icon:fe}),null)};w.displayName="MobileOutlined";w.inheritAttrs=!1;const be={class:"address-management-container"},ge={class:"page-header"},he={class:"header-actions"},_e={class:"address-list"},ye={key:0,class:"empty-state"},Ce={class:"empty-content"},xe={class:"empty-icon"},Oe={key:0,class:"default-tag"},ke={class:"address-info"},De={class:"contact-info"},Ne={class:"contact-name"},Pe={class:"contact-phone"},we={class:"location-info"},Ae={class:"region"},je={class:"detail-address"},ze={class:"address-actions"},Te={__name:"address",setup(c){const n=y(!1),s=y(!1),v=y(),o=y([]),l=re({id:null,contactName:"",phone:"",phoneType:"mobile",areaCode:"",phoneNumber:"",extension:"",regionCodes:[],region:"",detailAddress:"",isDefault:!1}),U=y([{value:"110000",label:"北京市",children:[{value:"110100",label:"北京市",children:[{value:"110101",label:"东城区"},{value:"110102",label:"西城区"},{value:"110105",label:"朝阳区"},{value:"110106",label:"丰台区"},{value:"110107",label:"石景山区"},{value:"110108",label:"海淀区"},{value:"110109",label:"门头沟区"},{value:"110111",label:"房山区"},{value:"110112",label:"通州区"},{value:"110113",label:"顺义区"},{value:"110114",label:"昌平区"},{value:"110115",label:"大兴区"},{value:"110116",label:"怀柔区"},{value:"110117",label:"平谷区"},{value:"110118",label:"密云区"},{value:"110119",label:"延庆区"}]}]},{value:"310000",label:"上海市",children:[{value:"310100",label:"上海市",children:[{value:"310101",label:"黄浦区"},{value:"310104",label:"徐汇区"},{value:"310105",label:"长宁区"},{value:"310106",label:"静安区"},{value:"310107",label:"普陀区"},{value:"310109",label:"虹口区"},{value:"310110",label:"杨浦区"},{value:"310112",label:"闵行区"},{value:"310113",label:"宝山区"},{value:"310114",label:"嘉定区"},{value:"310115",label:"浦东新区"},{value:"310116",label:"金山区"},{value:"310117",label:"松江区"},{value:"310118",label:"青浦区"},{value:"310120",label:"奉贤区"},{value:"310151",label:"崇明区"}]}]},{value:"440000",label:"广东省",children:[{value:"440100",label:"广州市",children:[{value:"440103",label:"荔湾区"},{value:"440104",label:"越秀区"},{value:"440105",label:"海珠区"},{value:"440106",label:"天河区"},{value:"440111",label:"白云区"},{value:"440112",label:"黄埔区"},{value:"440113",label:"番禺区"},{value:"440114",label:"花都区"},{value:"440115",label:"南沙区"},{value:"440117",label:"从化区"},{value:"440118",label:"增城区"}]},{value:"440300",label:"深圳市",children:[{value:"440303",label:"罗湖区"},{value:"440304",label:"福田区"},{value:"440305",label:"南山区"},{value:"440306",label:"宝安区"},{value:"440307",label:"龙岗区"},{value:"440308",label:"盐田区"},{value:"440309",label:"龙华区"},{value:"440310",label:"坪山区"},{value:"440311",label:"光明区"}]}]}]),V={contactName:[{required:!0,message:"请输入联系人姓名",trigger:"blur"},{min:2,max:20,message:"联系人姓名长度为2-20个字符",trigger:"blur"}],phone:[{validator:(i,e)=>{if(l.phoneType==="mobile"){if(!l.phone)return Promise.reject("请输入手机号码");if(!/^1[3-9]\d{9}$/.test(l.phone))return Promise.reject("请输入正确的手机号码")}else{if(!l.areaCode)return Promise.reject("请输入区号");if(!l.phoneNumber)return Promise.reject("请输入电话号码");if(!/^0\d{2,3}$/.test(l.areaCode))return Promise.reject("区号格式不正确，应为3-4位数字且以0开头");if(!/^\d{7,8}$/.test(l.phoneNumber))return Promise.reject("电话号码格式不正确，应为7-8位数字");if(l.extension&&!/^\d{1,6}$/.test(l.extension))return Promise.reject("分机号格式不正确，应为1-6位数字")}return Promise.resolve()},trigger:"blur"}],regionCodes:[{required:!0,message:"请选择所在地区",trigger:"change"}],detailAddress:[{required:!0,message:"请输入详细地址",trigger:"blur"},{min:5,max:100,message:"详细地址长度为5-100个字符",trigger:"blur"}]},B=()=>Date.now()+Math.random().toString(36).substr(2,9),I=()=>{o.value=[{id:"1",contactName:"张三",phone:"13800138000",phoneType:"mobile",regionCodes:["110000","110100","110105"],region:"北京市北京市朝阳区",detailAddress:"建国门外大街1号国贸大厦A座2001室",isDefault:!0},{id:"2",contactName:"李四",phone:"021-12345678",phoneType:"telephone",regionCodes:["310000","310100","310115"],region:"上海市上海市浦东新区",detailAddress:"陆家嘴环路1000号恒生银行大厦50楼",isDefault:!1}]},F=(i,e)=>{e&&e.length>0&&(l.region=e.map(u=>u.label).join(""))},q=i=>{var e;l.phone="",l.areaCode="",l.phoneNumber="",l.extension="",(e=v.value)==null||e.clearValidate(["phone"])},N=()=>{var i;Object.assign(l,{id:null,contactName:"",phone:"",phoneType:"mobile",areaCode:"",phoneNumber:"",extension:"",regionCodes:[],region:"",detailAddress:"",isDefault:!1}),(i=v.value)==null||i.resetFields()},A=()=>{s.value=!1,N(),n.value=!0},H=i=>{if(s.value=!0,Object.assign(l,{...i}),i.phoneType==="telephone"&&i.phone){const e=i.phone.split("-");e.length>=2&&(l.areaCode=e[0],l.phoneNumber=e[1],l.extension=e[2]||"",l.phone="")}n.value=!0},L=i=>{o.value.forEach(e=>{e.isDefault=e.id===i}),k.success("已设为默认地址")},G=i=>{const e=o.value.find(u=>u.id===i);ce.confirm({title:"确认删除",content:`确定要删除联系人"${e.contactName}"的地址吗？`,okText:"确定",cancelText:"取消",onOk(){const u=o.value.findIndex(f=>f.id===i);if(u>-1){const f=o.value[u].isDefault;o.value.splice(u,1),f&&o.value.length>0&&(o.value[0].isDefault=!0),k.success("地址删除成功")}}})},J=async()=>{try{await v.value.validate();let i="";l.phoneType==="mobile"?i=l.phone:(i=l.areaCode+"-"+l.phoneNumber,l.extension&&(i+="-"+l.extension));const e={...l,phone:i};if(s.value){const u=o.value.findIndex(f=>f.id===l.id);u>-1&&(l.isDefault&&o.value.forEach(f=>{f.id!==l.id&&(f.isDefault=!1)}),o.value[u]={...e},k.success("地址修改成功"))}else{const u={...e,id:B()};u.isDefault?o.value.forEach(f=>{f.isDefault=!1}):o.value.length===0&&(u.isDefault=!0),o.value.push(u),k.success("地址添加成功")}n.value=!1,N()}catch(i){console.error("表单验证失败:",i)}},Q=()=>{n.value=!1,N()};return ie(()=>{I()}),(i,e)=>{const u=p("a-button"),f=p("a-tag"),W=p("a-space"),X=p("a-card"),Y=p("a-col"),Z=p("a-row"),h=p("a-input"),_=p("a-form-item"),j=p("a-select-option"),K=p("a-select"),ee=p("a-input-group"),le=p("a-cascader"),ae=p("a-textarea"),te=p("a-checkbox"),ne=p("a-form"),oe=p("a-modal");return m(),C("div",be,[d("div",ge,[e[11]||(e[11]=d("div",{class:"header-content"},[d("h1",{class:"page-title"},"地址管理"),d("p",{class:"page-description"},"管理您的收货地址信息")],-1)),d("div",he,[a(u,{type:"primary",size:"large",onClick:A,class:"add-btn"},{icon:r(()=>[a(g(T))]),default:r(()=>[e[10]||(e[10]=b(" 新增地址 "))]),_:1})])]),d("div",_e,[o.value.length===0?(m(),C("div",ye,[d("div",Ce,[d("div",xe,[a(g(D))]),e[13]||(e[13]=d("h3",null,"暂无地址信息",-1)),e[14]||(e[14]=d("p",null,'您还没有添加任何地址，点击"新增地址"开始添加',-1)),a(u,{type:"primary",onClick:A},{icon:r(()=>[a(g(T))]),default:r(()=>[e[12]||(e[12]=b(" 新增地址 "))]),_:1})])])):(m(),x(Z,{key:1,gutter:[24,24]},{default:r(()=>[(m(!0),C(E,null,ue(o.value,t=>(m(),x(Y,{xs:24,sm:12,lg:8,key:t.id},{default:r(()=>[a(X,{class:de(["address-card",{"default-address":t.isDefault}])},{default:r(()=>[t.isDefault?(m(),C("div",Oe,[a(f,{color:"success"},{default:r(()=>e[15]||(e[15]=[b("默认地址")])),_:1})])):P("",!0),d("div",ke,[d("div",De,[d("h3",Ne,O(t.contactName),1),d("div",Pe,[a(g(M)),d("span",null,O(t.phone),1)])]),d("div",we,[d("div",Ae,[a(g(D)),d("span",null,O(t.region),1)]),d("div",je,O(t.detailAddress),1)])]),d("div",ze,[a(W,null,{default:r(()=>[t.isDefault?P("",!0):(m(),x(u,{key:0,type:"link",onClick:z=>L(t.id),class:"action-btn"},{default:r(()=>e[16]||(e[16]=[b(" 设为默认 ")])),_:2},1032,["onClick"])),a(u,{type:"link",onClick:z=>H(t),class:"action-btn"},{default:r(()=>e[17]||(e[17]=[b(" 编辑 ")])),_:2},1032,["onClick"]),a(u,{type:"link",danger:"",onClick:z=>G(t.id),class:"action-btn"},{default:r(()=>e[18]||(e[18]=[b(" 删除 ")])),_:2},1032,["onClick"])]),_:2},1024)])]),_:2},1032,["class"])]),_:2},1024))),128))]),_:1}))]),a(oe,{open:n.value,"onUpdate:open":e[9]||(e[9]=t=>n.value=t),title:s.value?"编辑地址":"新增地址",width:"600px",maskClosable:!1,onOk:J,onCancel:Q,class:"address-modal"},{default:r(()=>[a(ne,{ref_key:"formRef",ref:v,model:l,rules:V,layout:"vertical",class:"address-form"},{default:r(()=>[a(_,{label:"联系人",name:"contactName"},{default:r(()=>[a(h,{value:l.contactName,"onUpdate:value":e[0]||(e[0]=t=>l.contactName=t),placeholder:"请输入联系人姓名",size:"large"},null,8,["value"])]),_:1}),a(_,{label:"联系方式",name:"phone"},{default:r(()=>[a(ee,{compact:""},{default:r(()=>[a(K,{value:l.phoneType,"onUpdate:value":e[1]||(e[1]=t=>l.phoneType=t),style:{width:"110px"},size:"large",onChange:q},{default:r(()=>[a(j,{value:"mobile"},{default:r(()=>[a(g(w)),e[19]||(e[19]=b(" 手机 "))]),_:1}),a(j,{value:"telephone"},{default:r(()=>[a(g(M)),e[20]||(e[20]=b(" 座机 "))]),_:1})]),_:1},8,["value"]),l.phoneType==="mobile"?(m(),x(h,{key:0,value:l.phone,"onUpdate:value":e[2]||(e[2]=t=>l.phone=t),placeholder:"请输入手机号码",style:{width:"calc(100% - 110px)"},size:"large"},null,8,["value"])):(m(),C(E,{key:1},[a(h,{value:l.areaCode,"onUpdate:value":e[3]||(e[3]=t=>l.areaCode=t),placeholder:"区号",style:{width:"80px"},size:"large"},null,8,["value"]),a(h,{value:l.phoneNumber,"onUpdate:value":e[4]||(e[4]=t=>l.phoneNumber=t),placeholder:"电话号码",style:{width:"calc(100% - 300px)"},size:"large"},null,8,["value"]),a(h,{value:l.extension,"onUpdate:value":e[5]||(e[5]=t=>l.extension=t),placeholder:"分机(可选)",style:{width:"110px"},size:"large"},null,8,["value"])],64))]),_:1})]),_:1}),a(_,{label:"所在地区",name:"region"},{default:r(()=>[a(le,{value:l.regionCodes,"onUpdate:value":e[6]||(e[6]=t=>l.regionCodes=t),options:U.value,placeholder:"请选择省/市/区",size:"large",onChange:F},null,8,["value","options"])]),_:1}),a(_,{label:"详细地址",name:"detailAddress"},{default:r(()=>[a(ae,{value:l.detailAddress,"onUpdate:value":e[7]||(e[7]=t=>l.detailAddress=t),placeholder:"请输入详细地址（街道、门牌号等）",rows:3,size:"large"},null,8,["value"])]),_:1}),!s.value||!l.isDefault?(m(),x(_,{key:0,name:"isDefault"},{default:r(()=>[a(te,{checked:l.isDefault,"onUpdate:checked":e[8]||(e[8]=t=>l.isDefault=t)},{default:r(()=>e[21]||(e[21]=[b(" 设为默认地址 ")])),_:1},8,["checked"])]),_:1})):P("",!0)]),_:1},8,["model"])]),_:1},8,["open","title"])])}}},Re=se(Te,[["__scopeId","data-v-d46626ce"]]);export{Re as default};
